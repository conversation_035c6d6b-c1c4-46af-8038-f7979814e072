import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'
import presetRemToPx from '@unocss/preset-rem-to-px'

import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetUno,
  transformerDirectives,
} from 'unocss'

export default defineConfig({
  theme: {
    colors: {
      'primary': 'var(--el-color-primary)',
      'primary-lighter': 'var(--el-color-primary-light-9)',
      'desc': 'var(--color-desc)',
    },
  },
  shortcuts: [
    ['btn', 'px-4 py-1 rounded inline-block bg-teal-600 text-white cursor-pointer hover:bg-teal-700 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50'],
    ['icon-btn', 'text-[0.9em] inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-teal-600 !outline-none'],
  ],
  rules: [
    [/^fs-(.+)$/, ([, d]) => ({ 'font-size': `${d}px` })],
  ],
  presets: [
    presetRemToPx(),
    presetUno(),
    presetAttributify(),
    presetIcons({
      warn: true,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'top',
        'fill': 'currentColor',
      },
      collections: {
        custom: FileSystemIconLoader('./src/assets/icons', svg => svg),
      },
    }),
  ],
  transformers: [
    transformerDirectives(),
  ],
  safelist: (`mr-10% color-blue color-primary overflow-x-hidden i-custom-project justify-between justify-end pt-5 i-custom-mysql i-custom-clickhouse i-custom-hive i-custom-odbc i-custom-oracle i-custom-postgresql i-ph-folder-simple i-ph-grid-nine block! min-h-120 bg-green color-green i-custom-user i-custom-password`).split(' '),
})
