{"type": "module", "private": true, "packageManager": "pnpm@9.15.1", "scripts": {"dev": "vite --port 5555 --open --mode development", "build:porsche": "vite build --mode porsche", "build:lincoln": "vite build --mode lincoln", "build:prod": "vite build --mode production", "typecheck": "vue-tsc", "preview": "vite preview", "test": "vitest", "postinstall": "npx simple-git-hooks", "lint": "eslint . --fix"}, "dependencies": {"@codemirror/lang-sql": "^6.8.0", "@element-plus/icons-vue": "^2.3.1", "@hn/ui": "^1.0.3", "@hn/utils": "^0.0.16", "@vueuse/core": "^12.2.0", "axios": "^1.7.9", "codemirror": "^6.0.1", "crypto-js": "^4.2.0", "element-plus": "^2.9.2", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "nprogress": "^0.2.0", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.2.0", "qs": "^6.14.0", "sql-formatter": "^15.4.9", "thememirror": "^2.0.1", "vue": "^3.5.13", "vue-codemirror": "^6.1.1", "vue-router": "^4.5.0"}, "devDependencies": {"@antfu/eslint-config": "^3.12.1", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@iconify-json/carbon": "^1.2.5", "@iconify-json/ph": "^1.2.2", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash.clonedeep": "^4.5.9", "@types/node": "^22.10.2", "@types/qs": "^6.9.18", "@unocss/eslint-config": "^0.65.2", "@unocss/eslint-plugin": "^0.65.2", "@unocss/preset-rem-to-px": "^0.65.3", "@unocss/reset": "^0.65.2", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "code-inspector-plugin": "^0.19.1", "eslint": "^9.17.0", "eslint-plugin-format": "^0.1.3", "husky": "^9.1.7", "jsdom": "^25.0.1", "lint-staged": "^15.2.11", "pnpm": "^9.15.1", "sass-embedded": "^1.83.1", "simple-git-hooks": "^2.11.1", "typescript": "^5.7.2", "unocss": "^0.65.2", "unplugin-auto-import": "^0.19.0", "unplugin-element-plus": "^0.9.0", "unplugin-vue-components": "^0.28.0", "unplugin-vue-router": "^0.10.9", "vite": "^5.4.14", "vite-plugin-html": "^3.2.2", "vite-plugin-vue-layouts": "^0.11.0", "vitest": "^2.1.8", "vue-tsc": "^2.2.0"}, "resolutions": {"esbuild": "0.21.5", "unplugin": "^2.1.0", "vite": "^5.4.14"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "commit-msg": "npx --no-install commitlint --edit $1"}, "lint-staged": {"*.{vue,js,ts,tsx,jsx}": "eslint --fix"}}