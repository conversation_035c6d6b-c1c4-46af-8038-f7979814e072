---
before_script:
  - nvm use v17
  - pnpm install
#cache:
#  key: ${CI_BUILD_REF_NAME}
#  paths:
#    - node_modules/
#    - dist
build:
  stage: build
  script:
    - pnpm run build:prod
  artifacts:
    paths:
      - dist
  only:
    - /^feature-.*$/
    - /^hotfix-.*$/
    - /^release-.*$/
release:
  stage: build
  script:
    - pnpm run build:prod
  only:
    - /^release-.*$/
  when: manual
deploy:
  stage: deploy
  script:
    - pnpm run build:prod
  only:
    - tags
