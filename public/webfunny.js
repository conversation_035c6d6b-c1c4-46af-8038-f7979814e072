!(function (n) {
  const o = {}; function r(e) {
    if (o[e])
      return o[e].exports; const t = o[e] = { i: e, l: !1, exports: {} }; return n[e].call(t.exports, t, t.exports, r), t.l = !0, t.exports
  }r.m = n, r.c = o, r.d = function (e, t, n) { r.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: n }) }, r.r = function (e) { typeof Symbol != 'undefined' && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }), Object.defineProperty(e, '__esModule', { value: !0 }) }, r.t = function (t, e) {
    if (1 & e && (t = r(t)), 8 & e)
      return t; if (4 & e && typeof t == 'object' && t && t.__esModule)
      return t; const n = Object.create(null); if (r.r(n), Object.defineProperty(n, 'default', { enumerable: !0, value: t }), 2 & e && typeof t != 'string') {
      for (const o in t)r.d(n, o, ((e) => { return t[e] }).bind(null, o))
    } return n
  }, r.n = function (e) { const t = e && e.__esModule ? function () { return e.default } : function () { return e }; return r.d(t, 'a', t), t }, r.o = function (e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, r.p = '', r(r.s = 0)
}([function (e, t, n) { e.exports = n(1) }, function (e, t, n) {
  let o, w, r, i, a; !(function (T) {
    sessionStorage || (T.sessionStorage = {}), localStorage || (T.localStorage = {}), T.webfunnyRequests || (T.webfunnyRequests = []); const E = T.localStorage; const a = typeof navigator == 'object' && navigator.appName == 'Microsoft Internet Explorer' && Number.parseInt(navigator.appVersion.split(';')[1].replace(/ /g, '').replace('MSIE', '')) <= 9; let x = E.WF_CONFIG ? JSON.parse(E.WF_CONFIG) : { s: !0, ia: [''], wc: 40, pv: { s: true, ia: [''] }, je: { s: true, ia: [''] }, hl: { s: true, ia: [''], uh: !1, rl: 2e3, sl: 2e3 }, rl: { s: true, ia: [''] }, bl: { s: true }, lc: { s: true }, sc: { r: 100, c: 3 } }; let i = T.location.href.split('?')[0]; const b = T.performance ? T.performance.timing : {}; const N = T.performance && typeof T.performance.getEntries == 'function' ? T.performance.getEntries() : null; const e = '3.2.14'; const t = !T.location.href.includes('https') ? 'http://' : 'https://'; const s = T.location.href; const I = encodeURIComponent(T.location.pathname); const n = t + (T.webfunnyDomain || 'fiat-webfunny.chinahuanong.com.cn'); const o = '/server/upLog'; const r = '/server/upDLog'; const c = n + o; const f = n + r; const _ = 'CUSTOMER_PV'; const u = 'STAY_TIME'; const d = 'CUS_LEAVE'; const C = 'PAGE_LOAD'; const S = 'HTTP_LOG'; const l = 'JS_ERROR'; const p = 'SCREEN_SHOT'; const O = 'ELE_BEHAVIOR'; const M = 'RESOURCE_LOAD'; const h = 'CUSTOMIZE_BEHAVIOR'; const g = 'VIDEOS_EVENT'; const k = 'LAST_BROWSE_DATE'; const L = 'WM_PAGE_ENTRY_TIME'; const $ = 'WM_VISIT_PAGE_COUNT'; var D = new function () {
      this.checkIgnore = function (t, n) {
        if (!n)
          return !0; try { for (var e = n.replace(/ /g, ''), o = x[t].ia || [], r = !0, i = 0; i < o.length; i++) { const a = o[i].replace(/ /g, ''); if (a && e.includes(a)) { r = !1; break } } return r }
        catch (e) { console.error(`checkIgnore异常，key: ${t};str:${n}`) }
      }, this.checkHttpReqResLen = function (e, t) {
        const n = x.hl; const o = Number.parseInt(n.rl, 10) || 2e3; const r = Number.parseInt(n.sl, 10) || 2e3; const i = t === 'req' ? o : r; let a = ''; if (e && e.length < i) {
          try { a = e }
          catch (e) { a = '' }
        }
        else {
          a = '内容太长'
        } return a
      }, this.getIp = function (e) {}, this.getUuid = function () { const e = D.formatDate((new Date()).getTime(), 'yMdhms'); return `${'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (e) => { const t = 16 * Math.random() | 0; return (e == 'x' ? t : 3 & t | 8).toString(16) })}-${e}` }, this.getKeyByWebMonitorId = function (e) {
        let t = ''; let n = 'old'; const o = this.getUuid(); const r = D.getWfCookie('monitorCustomerKeys'); const i = (new Date()).getTime() + 31104e7; if (r) { const a = JSON.parse(r); a[e] ? t = a[e] : (a[e] = o, D.setWfCookie('monitorCustomerKeys', JSON.stringify(a), i), t = o, n = 'new') }
        else { const s = {}; s[e] = o, D.setWfCookie('monitorCustomerKeys', JSON.stringify(s), i), t = o, n = 'new' } return { customerKey: t, status: n }
      }, this.setWfCookie = function (e, t, n) {
        const o = { data: t, expires: n }; if (E.WEBFUNNY_COOKIE) { const r = JSON.parse(E.WEBFUNNY_COOKIE); r[e] = o, E.WEBFUNNY_COOKIE = JSON.stringify(r) }
        else { const i = {}; i[e] = o, E.WEBFUNNY_COOKIE = JSON.stringify(i) }
      }, this.getWfCookie = function (e) { let t = null; if (E.WEBFUNNY_COOKIE) { const n = (t = JSON.parse(E.WEBFUNNY_COOKIE))[e]; return n ? Number.parseInt(n.expires, 10) < (new Date()).getTime() ? (delete t[e], E.WEBFUNNY_COOKIE = JSON.stringify(t), '') : n.data : '' } return '' }, this.getCusInfo = function (e) {
        if (!e)
          return ''; const t = (E.wmUserInfo ? JSON.parse(E.wmUserInfo) : {})[e]; return t || ''
      }, this.getWebMonitorId = function () { const e = 'webfunny_20250516_104255_pro'; let t = sessionStorage.CUSTOMER_WEB_MONITOR_ID || e; if (/^webfunny\d*(_\d{8}_\d{6}(_[a-z]+)?)?$/.test(t) || (t = e), t.includes('_pro')) { const n = D.getCusInfo('env'); n && (t = t.replace('_pro', `_${n}`)) } return t }, this.isTodayBrowse = function (e) { const t = E[e]; const n = `${(new Date()).getFullYear()}-${(new Date()).getMonth() + 1}-${(new Date()).getDate()}`; return t && n == t ? !(!t || n != t) : (E[e] = n, !1) }, this.formatDate = function (e, t) { const n = new Date(e).getFullYear(); let o = new Date(e).getMonth() + 1; let r = new Date(e).getDate(); let i = new Date(e).getHours(); let a = new Date(e).getMinutes(); let s = new Date(e).getSeconds(); return o = o > 9 ? o : `0${o}`, r = r > 9 ? r : `0${r}`, i = i > 9 ? i : `0${i}`, a = a > 9 ? a : `0${a}`, s = s > 9 ? s : `0${s}`, t.replace('y', n).replace('M', o).replace('d', r).replace('h', i).replace('m', a).replace('s', s) }, this.getPageKey = function () { const e = this.getUuid(); return E.monitorPageKey && /^[0-9a-z]{8}(-[0-9a-z]{4}){3}-[0-9a-z]{12}-\d{13}$/.test(E.monitorPageKey) || (E.monitorPageKey = e), E.monitorPageKey }, this.setPageKey = function () { E.monitorPageKey = this.getUuid() }, this.addLoadEvent = function (e) { const t = T.onload; typeof T.onload != 'function' ? T.onload = e : T.onload = function () { t(), e() } }, this.addOnBeforeUnloadEvent = function (e) { const t = T.onbeforeunload; typeof T.onbeforeunload != 'function' ? T.onbeforeunload = e : T.onbeforeunload = function () { t(), e() } }, this.addOnclickForDocument = function (t) { const n = document.onclick; typeof document.onclick != 'function' ? document.onclick = t : document.onclick = function (e) { n(), t(e) } }, this.ajax = function (e, t, n, o, r) {
        try {
          const i = T.XMLHttpRequest ? new XMLHttpRequest() : new ActiveXObject('Microsoft.XMLHTTP'); i.open(e, t, !0), i.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded'), i.onreadystatechange = function () {
            if (i.readyState == 4) {
              let t = {}; try { t = i.responseText ? JSON.parse(i.responseText) : {} }
              catch (e) { t = {} } typeof o == 'function' && o(t)
            }
          }, i.onerror = function (e) { console.warn(`【【 ${t} 上报接口不通，请处理。】】`, e), typeof r == 'function' && r() }; const a = JSON.stringify(n || {}); i.send(`data=${a}`)
        }
        catch (e) { console.log(e) }
      }, this.upLog = function (e, i) {
        if (e && e != 'undefined') {
          if (a) { (new Image()).src = `${c}?logInfo=${e}`; for (let t = 0; t < R.length; t++)E[R[t]] = '' }
          else {
            D.ajax('POST', c, { logInfo: e }, (e) => {
              if (e && e.data && e.data.d) { E.ds = e.data.d == 'c' ? 'connected' : 'disconnect'; const t = e.data.c; if (t) { E.setItem('WF_CONFIG', e.data.c); const n = JSON.parse(t); if ((x = n).s == 0) { const o = (new Date()).getTime() + 6e5; D.setWfCookie('webfunnyStart', 'p', o) } } } if (!0 === i) {
                for (let r = 0; r < R.length; r++)E[R[r]] = ''
              }
            }, () => {
              if (!0 === i) {
                for (let e = 0; e < R.length; e++)E[R[e]] = ''
              }
            })
          }
        }
      }, this.initDebugTool = function () {
        const a = D.getCusInfo('userId'); function t(e) {
          for (var t = [], n = e.length, o = 0; o < n; o++)t.push(e[o]); const r = {}; r.log = t, r.userId = a, r.happenTime = (new Date()).getTime(); let i = ''; try { i = D.b64Code(JSON.stringify(r)) }
          catch (e) { i = 'convert fail' } return i
        } const n = console.log; const o = console.warn; console.log = function () { const e = t(arguments); return E.ds === 'connected' && D.ajax('POST', f, { consoleInfo: e }, () => {}), n.apply(console, arguments) }, console.warn = function () { const e = t(arguments); return E.ds === 'connected' && D.ajax('POST', f, { warnInfo: e }, () => {}), o.apply(console, arguments) }
      }, this.uploadLocalInfo = function () {
        const e = D.getCusInfo('userId'); let t = {}; for (var n in E) typeof E[n] == 'function' || R.includes(n) || E[n].length > 1e3 || (t[n] = E[n]); try { t = D.b64Code(JSON.stringify(t)) }
        catch (e) { t = '' } let o = {}; for (var n in sessionStorage) typeof sessionStorage[n] == 'function' || R.includes(n) || sessionStorage[n].length > 1e3 || (o[n] = sessionStorage[n]); try { o = D.b64Code(JSON.stringify(o)) }
        catch (e) { o = '' } const r = D.b64Code(document.cookie); D.ajax('POST', f, { localInfo: t, sessionInfo: o, cookieInfo: r, userId: e || 'userId' }, (e) => { if ((setTimeout(() => { D.uploadLocalInfo() }, 2e4), e.data) && e.data.clear == 1) { const t = E.wmUserInfo; localStorage.clear(), localStorage.wmUserInfo = t, sessionStorage.clear(), E.WEBFUNNY_COOKIE = '' } })
      }, this.encryptObj = function (e) { if (Array.isArray(e)) { for (var t = [], n = 0; n < e.length; ++n)t[n] = this.encryptObj(e[n]); return t } if (e instanceof Object) { t = {}; for (var n in e)t[n] = this.encryptObj(e[n]); return t } return (e += '').length > 50 && (e = `${e.substring(0, 10)}****${e.substring(e.length - 9, e.length)}`), e }, this.getDevice = function () {
        const e = {}; const t = navigator.userAgent; const n = t.match(/(Android);?[\s/]+([\d.]+)?/); const o = t.match(/(iPad).*OS\s([\d_]+)/); const r = !o && t.match(/(iPhone\sOS)\s([\d_]+)/); const i = t.match(/Android\s[\s\S]+Build\//); const a = T.screen.width; const s = T.screen.height; if (e.ios = e.android = e.iphone = e.ipad = e.androidChrome = !1, e.isWeixin = /MicroMessenger/i.test(t), e.os = 'web', e.deviceName = 'PC', e.deviceSize = `${a}×${s}`, n && (e.os = 'android', e.osVersion = n[2], e.android = !0, e.androidChrome = t.toLowerCase().includes('chrome')), (o || r) && (e.os = 'ios', e.ios = !0), r && (e.osVersion = r[2].replace(/_/g, '.'), e.iphone = !0), o && (e.osVersion = o[2].replace(/_/g, '.'), e.ipad = !0), e.ios && e.osVersion && t.includes('Version/') && e.osVersion.split('.')[0] === '10' && (e.osVersion = t.toLowerCase().split('version/')[1].split(' ')[0]), e.iphone) { let c = 'iphone'; a === 320 && s === 480 ? c = '4' : a === 320 && s === 568 ? c = '5/SE' : a === 375 && s === 667 ? c = '6/7/8' : a === 414 && s === 736 ? c = '6/7/8 Plus' : a === 375 && s === 812 ? c = 'X/S/Max' : a === 414 && s === 896 ? c = '11/Pro-Max' : a === 375 && s === 812 ? c = '11-Pro/mini' : a === 390 && s === 844 ? c = '12/Pro' : a === 428 && s === 926 && (c = '12-Pro-Max'), e.deviceName = `iphone ${c}` }
        else if (e.ipad) {
          e.deviceName = 'ipad'
        }
        else if (i) { for (var f = i[0].split(';'), u = '', d = 0; d < f.length; d++)f[d].includes('Build') && (u = f[d].replace(/Build\//g, '')); u == '' && (u = f[1]), e.deviceName = u.replace(/(^\s*)|(\s*$)/g, '') } if (!t.includes('Mobile')) {
          const l = navigator.userAgent.toLowerCase(); if (e.browserName = '其他', l.indexOf('msie') > 0) { var p = l.match(/msie [\d.]+;/gi)[0]; e.browserName = 'ie', e.browserVersion = p.split('/')[1] }
          else if (l.indexOf('edg') > 0) { p = l.match(/edg\/[\d.]+/gi)[0]; e.browserName = 'edge', e.browserVersion = p.split('/')[1] }
          else if (l.indexOf('firefox') > 0) { p = l.match(/firefox\/[\d.]+/gi)[0]; e.browserName = 'firefox', e.browserVersion = p.split('/')[1] }
          else if (l.indexOf('safari') > 0 && !l.includes('chrome')) { p = l.match(/safari\/[\d.]+/gi)[0]; e.browserName = 'safari', e.browserVersion = p.split('/')[1] }
          else if (l.indexOf('chrome') > 0) { p = l.match(/chrome\/[\d.]+/gi)[0]; e.browserName = 'chrome', e.browserVersion = p.split('/')[1], l.indexOf('360se') > 0 && (e.browserName = '360') }
        } return e.webView = (r || o) && t.match(/.*AppleWebKit(?!.*Safari)/i), e
      }, this.loadJs = function (e, t, n) { const o = document.createElement('script'); o.async = 1, o.src = e, o.onload = t, typeof n == 'function' && (o.onerror = n); const r = document.getElementsByTagName('script')[0]; return r.parentNode.insertBefore(o, r), r }, this.b64Code = function (e) {
        const t = encodeURIComponent(e); try { return btoa(encodeURIComponent(t).replace(/%([0-9A-F]{2})/g, (e, t) => { return String.fromCharCode(`0x${t}`) })) }
        catch (e) { return t }
      }, this.perfSubtract = function (e, t) { return e === 0 || t === 0 ? 0 : e - t }
    }(); const W = (new Date()).getTime() + 6048e5; const v = D.getDevice(); var R = [O, l, S, p, _, C, M, h, g]; const A = [o, r, '/upLogs', '/upEvent', '/upEvents', '/initCf']; const V = { ACTIVE_TIME: {} }; let j = []; let P = 0; let J = ''; function w() { this.handleLogInfo = function (e, t) { if (t) { const n = E[e] ? E[e] : ''; switch (e) { case O:E[O] = `${n + JSON.stringify(t)}$$$`; break; case l:E[l] = `${n + JSON.stringify(t)}$$$`; break; case S:E[S] = `${n + JSON.stringify(t)}$$$`; break; case p:E[p] = `${n + JSON.stringify(t)}$$$`; break; case _:E[_] = `${n + JSON.stringify(t)}$$$`; break; case C:E[C] = `${n + JSON.stringify(t)}$$$`; break; case M:E[M] = `${n + JSON.stringify(t)}$$$`; break; case h:E[h] = `${n + JSON.stringify(t)}$$$`; break; case g:E[g] = `${n + JSON.stringify(t)}$$$` } } } } function y() { this.wmVersion = e, this.h = (new Date()).getTime(), this.a = D.getWebMonitorId(), this.g = D.b64Code(T.location.href.split('?')[0]), this.f = D.b64Code(T.location.href), this.b = D.getKeyByWebMonitorId(D.getWebMonitorId()).customerKey, this.c = D.getCusInfo('userId'), this.j = D.b64Code(D.getCusInfo('projectVersion')), this.d = D.b64Code(D.getCusInfo('userTag')), this.e = D.b64Code(D.getCusInfo('secondUserParam')) } function U(e, t, n, o, r) { y.apply(this), this.i = e, this.k = D.getPageKey(), this.l = v.deviceName, this.deviceSize = v.deviceSize, this.m = v.os + (v.osVersion ? ` ${v.osVersion}` : ''), this.n = v.browserName, this.o = v.browserVersion, this.p = D.getWfCookie('wf_ip'), this.q = '', this.r = D.getWfCookie('wf_prov'), this.s = '', this.t = t, this.u = n, this.newStatus = o, this.referrer = (r || '').split('?')[0] } function K(e) { this.i = d, this.a = D.getWebMonitorId(), this.leaveType = e, this.h = (new Date()).getTime(), this.g = D.b64Code(T.location.href.split('?')[0]), this.b = D.getKeyByWebMonitorId(D.getWebMonitorId()).customerKey } function m(e, t) { y.apply(this), this.i = u, this.h = (new Date()).getTime(), this.a = D.getWebMonitorId(), this.g = D.b64Code(T.location.href.split('?')[0]), this.b = D.getKeyByWebMonitorId(D.getWebMonitorId()).customerKey, this.stayTime = e, this.activeTime = t } function B(e, t, n, o, r, i, a, s, c, f, u, d, l) { y.apply(this), this.i = e, this.t = t, this.firstByte = n, this.w = o, this.pageCompleteLoaded = r, this.dns = i, this.tcp = a, this.ssl = s, this.response = c, this.conTrans = f, this.domAnalysis = u, this.resourceLoaded = d, this.effectiveType = l, this.m = v.os } function F(e, t, n, o, r, i, a) { y.apply(this), this.i = e, this.da = t, this.G = D.b64Code(n), this.H = D.b64Code(o), this.I = D.b64Code(r), this.L = i, this.M = D.b64Code(a) } function H(e, t, n, o, r) { y.apply(this), this.i = e, this.O = t, this.k = D.getPageKey(), this.l = v.deviceName, this.m = v.os + (v.osVersion ? ` ${v.osVersion}` : ''), this.n = v.browserName, this.o = v.browserVersion, this.p = D.getWfCookie('wf_ip'), this.q = '', this.r = D.getWfCookie('wf_prov'), this.s = '', this.simpleErrorMessage = D.b64Code(n), this.P = D.b64Code(o), this.Q = D.b64Code(r), this.R = D.b64Code(navigator.userAgent) } function q(e, t, n, o, r, i, a, s, c, f, u, d) { y.apply(this), this.i = e, this.method = t, this.g = D.b64Code(o), this.S = D.b64Code(r), this.T = i, this.U = a, this.V = s, this.headerText = D.b64Code(n), this.W = D.b64Code(c), this.X = D.b64Code(f), this.h = u, this.u = d } function Y(e, t, n, o) { y.apply(this), this.i = e, this.Y = D.b64Code(t), this.Z = n, this.aa = o || 'jpeg' } function z(e, t, n, o) { y.apply(this), this.i = e, this.ba = n, this.ca = D.b64Code(t), this.T = o } function G(e, t, n, o, r) { this.c = e, this.a = D.getWebMonitorId(), this.da = t, this.ea = n, this.i = o, this.Y = r, this.h = (new Date()).getTime() } function X() { const e = Number.parseInt(E[L], 10); const t = (new Date()).getTime() - e; const n = D.getWfCookie('ACTIVE_TIME_INFO') || {}; let o = n.ACTIVE_TIME ? 1 * n.ACTIVE_TIME[I] : t; o === 0 && t <= 1e4 && (o = t); const r = JSON.stringify(new m(t, o)); navigator && typeof navigator.sendBeacon == 'function' && navigator.sendBeacon(c, r) } function Z() {
      V.ACTIVE_TIME[I] = 0, D.setWfCookie('ACTIVE_TIME_INFO', V, W); let h = new Date(); let g = h.getFullYear(); let v = h.getMonth(); let w = h.getDate(); try {
        const e = x.pv; const t = x.je; const n = x.hl; const o = x.rl; const r = x.bl; e.s && (Q(), D.addLoadEvent(() => { setTimeout(() => { if (N) { let e = 'load'; e = N[0] && N[0].type === 'navigate' ? 'load' : 'reload'; const t = b; const n = t.fetchStart; const o = D.perfSubtract(t.responseStart, n); const r = D.perfSubtract(t.domContentLoadedEventEnd, n); const i = D.perfSubtract(t.loadEventStart, n); const a = D.perfSubtract(t.domainLookupEnd, t.domainLookupStart); const s = D.perfSubtract(t.connectEnd, t.connectStart); const c = D.perfSubtract(t.connectEnd, t.secureConnectionStart); const f = D.perfSubtract(t.responseStart, t.requestStart); const u = D.perfSubtract(t.responseEnd, t.responseStart); const d = D.perfSubtract(t.domInteractive, t.responseEnd); const l = D.perfSubtract(t.loadEventStart, t.domContentLoadedEventEnd); const p = navigator && navigator.connection && navigator.connection.effectiveType || 'unknown'; const h = new B(C, e, o, r, i, a, s, c, f, u, d, l, p); h.handleLogInfo(C, h) } }, 1e3) }), !1 === a && (function () { if (T.Event && typeof T.Event == 'function') { function e(e) { const t = history[e]; const n = new Event(e); return function () { const e = t.apply(this, arguments); return n.arguments = arguments, T.dispatchEvent(n), e } }history.pushState = e('pushState'), history.replaceState = e('replaceState') }T.addEventListener('hashchange', () => { Q(1) }), T.addEventListener('popstate', () => { const e = T.location.href.split('?')[0].split('#')[0]; i != e && (Q(0), i = e) }), T.addEventListener('pushState', (e) => { Q(0) }), T.addEventListener('replaceState', (e) => { Q(0) }) }())), t.s && (function () {
          if (T.onerror = function (e, t, n, o, r) { ee('on_error', e, t, n, o, r ? r.stack : null) }, !0 === a)
            return; const o = console.error; console.error = function (e) {
            let t = e && e.message || e; const n = e && e.stack; if (n) {
              ee('on_error', t, s, 0, 0, n)
            }
            else {
              if (typeof t == 'object') {
                try { t = JSON.stringify(t) }
                catch (e) { t = '错误无法解析' }
              }ee('console_error', t, s, 0, 0, `CustomizeError: ${t}`)
            } return o.apply(console, arguments)
          }, T.onunhandledrejection = function (e) { let t = ''; let n = ''; n = typeof e.reason == 'object' ? (t = e.reason.message, e.reason.stack) : (t = e.reason, ''), t === ': ' && (t = n), ee('on_error', t, s, 0, 0, `UncaughtInPromiseError: ${n}`) }
        }()), n.s && !1 === a && (function () {
          const i = {}; function a(e) { const t = new CustomEvent(e, { detail: this }); T.dispatchEvent(t) } const s = T.XMLHttpRequest; function r(e, t) {
            if (w[e] && !0 !== w[e].uploadFlag) {
              const n = D.checkHttpReqResLen(t, 'res'); const o = w[e].simpleUrl; const r = (new Date()).getTime(); const i = w[e].event.detail.responseURL; const a = w[e].event.detail.status; const s = w[e].event.detail.statusText; const c = r - w[e].timeStamp; if (i) {
                for (var f = !1, u = 0; u < A.length; u++) {
                  if (i.includes(A[u])) { f = !0; break }
                } if (!0 !== f && D.checkIgnore('hl', i)) { const d = w[e].event.detail.wfHttpRecord; const l = D.checkHttpReqResLen(d.requestBody, 'req'); const p = d.method || ''; const h = JSON.stringify(d.header || {}); const g = new q(S, p, '', o, i, a, s, 'request', '', '', w[e].timeStamp, 0); const v = new q(S, p, h, o, i, a, s, 'response', l, n, r, c); j.push(g, v), w[e].uploadFlag = !0 }
              }
            }
          } var w = []; T.XMLHttpRequest = function () { const o = new s(); o.wfTraceId = D.getUuid(), i[o.wfTraceId] = { method: '', url: '', async: '', header: {}, requestBody: '' }; const e = o.onreadystatechange; o.onreadystatechange = e && typeof e == 'function' ? function () { return e.apply(o, arguments) } : function () { if (this.readyState === 4 && !1 === this.wfHttpRecord.async) { const e = this.wfHttpRecord.sendTime; const t = this.wfHttpRecord.method; const n = this.wfHttpRecord.url; const o = n.split('?')[0]; const r = JSON.stringify(this.wfHttpRecord.header || {}); const i = D.checkHttpReqResLen(this.wfHttpRecord.requestBody || '', 'req'); const a = D.checkHttpReqResLen(this.responseText || '', 'res'); const s = (new Date()).getTime(); const c = this.status; const f = this.statusText; const u = s - e; const d = new q(S, t, '', o, n, c, f, 'request', '', '', e, 0); const l = new q(S, t, r, o, n, c, f, 'response', i, a, s, u); j.push(d, l) } }; const n = o.setRequestHeader; o.setRequestHeader = function () { if (arguments.length && o.wfTraceId && i[o.wfTraceId]) { const e = arguments[0]; const t = arguments[1]; i[o.wfTraceId].header[e] = t } return n.apply(o, arguments) }; const r = o.open; r && (o.open = function () { if (arguments.length && o.wfTraceId && i[o.wfTraceId]) { const e = arguments[0]; const t = arguments[1]; const n = arguments[2]; i[o.wfTraceId].method = e, i[o.wfTraceId].url = t, i[o.wfTraceId].async = n } return r.apply(o, arguments) }); const t = o.send; return t && (o.send = function () { if (arguments.length && o.wfTraceId && i[o.wfTraceId]) { const e = arguments[0]; i[o.wfTraceId].requestBody = e } return this.wfHttpRecord = i[o.wfTraceId] || {}, this.wfHttpRecord && (this.wfHttpRecord.sendTime = (new Date()).getTime()), delete i[o.wfTraceId], t.apply(o, arguments) }), o.addEventListener('loadstart', function () { a.call(this, 'ajaxLoadStart') }, !1), o.addEventListener('loadend', function () { a.call(this, 'ajaxLoadEnd') }, !1), o }, T.addEventListener('ajaxLoadStart', (e) => { const t = { timeStamp: (new Date()).getTime(), event: e, simpleUrl: T.location.href.split('?')[0], uploadFlag: !1 }; w.push(t) }), T.addEventListener('ajaxLoadEnd', () => {
            for (var o = 0; o < w.length; o++) {
              if (!0 !== w[o].uploadFlag) {
                if (w[o].event.detail.status > 0) {
                  if ((`${w[o].event.detail.responseType}`).toLowerCase() === 'blob') {
                    !(function (t) {
                      let n = new FileReader(); n.onload = function () { const e = n.result; r(t, e) }; try { n.readAsText(w[o].event.detail.response, 'utf-8') }
                      catch (e) { r(t, `${w[o].event.detail.response }`) }
                    }(o))
                  }
                  else {
                    try {
                      const e = w[o] && w[o].event && w[o].event.detail; if (!e)
                        return; const t = e.responseType; let n = ''; t !== '' && t !== 'text' || (n = e.responseText), t === 'json' && (n = JSON.stringify(e.response)), r(o, n)
                    }
                    catch (e) {}
                  }
                }
              }
            }
          })
        }()), o.s && !1 === a && T.addEventListener('error', (e) => { const t = e.target.localName; let n = ''; if (t === 'link' ? n = e.target.href : t === 'script' && (n = e.target.src), n = n ? n.split('?')[0] : '', D.checkIgnore('rl', n) && !n.includes('pv.sohu.com/cityjson')) { const o = new z(M, n, t, '0'); o.handleLogInfo(M, o) } }, !0), r.s && !1 === a && D.addOnclickForDocument((e) => { if (e) { let t = ''; let n = ''; let o = ''; const r = e.target.tagName; let i = ''; e.target.tagName != 'svg' && e.target.tagName != 'use' && (t = e.target.className, n = e.target.placeholder || '', o = e.target.value || '', (i = e.target.innerText ? e.target.innerText.replace(/\s*/g, '') : '').length > 100 && (i = `${i.substring(0, 50)} ... ${i.substring(i.length - 49, i.length - 1)}`), i = i.replace(/\s/g, '')); const a = new F(O, 'click', t, n, o, r, i); a.handleLogInfo(O, a) } }), D.addOnBeforeUnloadEvent(() => { X() }); let y = 0; const m = R; setInterval(() => {
          let e = Number.parseInt(x.wc || '40', 10); if (e = E.ds == 'connected' ? 5 : e, y > 0 && y % 5 == 0) {
            if (j.length >= 10) { for (var t = '', n = 0; n < j.length; n++) { const o = j[n]; o && (t += `${JSON.stringify(o)}$$$`) }D.upLog(t, !1) }
            else { let r = ''; for (n = 0; n < j.length; n++) { const i = j[n]; i && (r += `${JSON.stringify(i)}$$$`) }E[S] += r, E[S].length >= 3e4 && (D.upLog(E[S], !1), E[S] = '') }j = []
          } if (e <= y) { let a = ''; for (n = 0; n < m.length; n++)a += E[m[n]] || ''; a.length > 0 && D.upLog(a, !0), y = 0; const s = D.getWfCookie('ACTIVE_TIME_INFO') || {}; let c = {}; s.ACTIVE_TIME ? c = s.ACTIVE_TIME : s.ACTIVE_TIME = {}; const f = c[I] || 0; a.length > 0 && (s.ACTIVE_TIME[I] = 1 * f + 200 * e), D.setWfCookie('ACTIVE_TIME_INFO', s, W); const u = new Date((new Date()).getTime() + 1e4); const d = u.getFullYear(); const l = u.getMonth(); const p = u.getDate(); (g < d || v < l || w < p) && (X(), E[L] = (new Date()).getTime(), h = new Date(), g = h.getFullYear(), v = h.getMonth(), w = h.getDate()) }y++
        }, 200)
      }
      catch (e) { console.error('监控代码异常，捕获', e) }
    } function Q(e) {
      const t = encodeURIComponent(T.location.href.split('?')[0]); const n = (new Date()).getTime(); if (t === J && n - P < 300) {
        P = n
      }
      else {
        P = n, J = t; const o = x.lc; o && !0 === o.s && D.getIp(), D.setPageKey(); const r = D.isTodayBrowse(k); E[L] = n; var i = null; const a = D.formatDate(n, 'y-M-d'); const s = E[$]; if (s) { const c = s.split('$$$'); const f = c[0]; const u = c[1]; const d = Number.parseInt(c[2], 10); a == u ? t != f && d == 1 && (E[$] = `${t}$$$${a}$$$2`, i = new K(2)) : (E[$] = `${t}$$$${a}$$$1`, i = new K(1)) }
        else {
          E[$] = `${t}$$$${a}$$$1`, i = new K(1)
        } var l = ''; N && (l = N[0] && N[0].type === 'navigate' ? 'load' : 'reload'); const p = D.getKeyByWebMonitorId(D.getWebMonitorId()); const h = p.customerKey; if (p.status === 'new') {
          g = 'n_uv'
        }
        else { var g = ''; const v = h ? h.match(/\d{14}/g) : []; if (v && v.length > 0) { const w = v[0].match(/\d{2}/g); const y = `${w[0] + w[1]}-${w[2]}-${w[3]} ${w[4]}:${w[5]}:${w[6]}`; const m = new Date(y).getTime(); const b = (new Date()).getTime(); g = b - m > 2e3 ? r == 0 ? 'o_uv' : 'o' : 'n_uv' } } const I = document.referrer ? document.referrer.split('?')[0] : ''; var C = D.b64Code(I); const S = E.ds; S || !0 !== o.s ? (S === 'connected' && D.initDebugTool(), setTimeout(() => { S === 'connected' && D.uploadLocalInfo() }, 2e3), O(e)) : D.getIp(() => { O() })
      } function O(n) { const e = T.location.href; function t() { const e = new U(_, l, 0, g, C); let t = `${JSON.stringify(e)}$$$`; i && (t += `${JSON.stringify(i)}$$$`), n ? e.handleLogInfo(_, e) : D.upLog(t, !1) }D.checkIgnore('pv', e) && (D.getCusInfo('userId') ? t() : setTimeout(() => { t() }, 3e3)) }
    } function ee(e, t, n, o, r, i) {
      let a = t || ''; let s = i || ''; let c = ''; let f = ''; if ((a.length !== 0 || s.length !== 0) && (a.length >= 1e3 && (a = a.substring(0, 999)), s.length >= 3e3 && (s = s.substring(0, 2999)), a.length >= 80 ? f = a.substring(0, 80) : a.length > 0 && a.length < 80 && (f = a), D.checkIgnore('je', a))) {
        if (a) {
          if (typeof s == 'string')
            c = s.split(': ')[0].replace('"', ''); else c = JSON.stringify(s).split(': ')[0].replace('"', '')
        } const u = new H(l, e, `${c}: ${f}`, `${c}: ${a}`, s); u.handleLogInfo(l, u)
      }
    }U.prototype = new w(), K.prototype = new w(), m.prototype = new w(), B.prototype = new w(), F.prototype = new w(), H.prototype = new w(), q.prototype = new w(), Y.prototype = new w(), z.prototype = new w(), G.prototype = new w(), new w(); for (var te = x.ia || [], ne = !1, oe = 0; oe < te.length; oe++) { const re = te[oe].replace(/ /g, ''); if (re && (T.location.href + T.location.hash).includes(re)) { ne = !0; break } } const ie = D.getWfCookie('webfunnyStart') || x.s; if (ie && ie != 'p' && !ne) {
      const ae = D.getWfCookie('samplingChoose'); if (ae === 2)
        return; if (ae === 1) {
        Z()
      }
      else {
        const se = x.sc || { r: 100, c: 3 }; const ce = se.r; const fe = 1 * se.c; if (ce < 100) { const ue = Math.ceil(100 * Math.random()); const de = (new Date()).getTime() + 24 * fe * 3600 * 1e3; ue <= ce ? (D.setWfCookie('samplingChoose', 1, de), Z()) : D.setWfCookie('samplingChoose', 2, de) }
        else {
          Z()
        }
      }
    }T.webfunny = { getCustomerKey() { return D.getKeyByWebMonitorId(D.getWebMonitorId()).customerKey }, wm_upload_picture(e, t, n) { const o = new Y(p, t, e, n || 'jpeg'); o.handleLogInfo(p, o) }, wm_upload_extend_log(e, t, n, o, r) { const i = new G(e, t, n, o, r); i.handleLogInfo(h, i) } }, (function () {
      if (typeof T.CustomEvent == 'function')
        return; function e(e, t) { t = t || { bubbles: !1, cancelable: !1, detail: void 0 }; const n = document.createEvent('CustomEvent'); return n.initCustomEvent(e, t.bubbles, t.cancelable, t.detail), n }e.prototype = T.Event.prototype, T.CustomEvent = e
    }())
  }(window)), window.LZString = (w = String.fromCharCode, r = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$', i = {}, a = { compressToEncodedURIComponent(e) { return e == null ? '' : a._compress(e, 6, (e) => { return r.charAt(e) }) }, decompressFromEncodedURIComponent(t) { return t == null ? '' : t == '' ? null : (t = t.replace(/ /g, '+'), a._decompress(t.length, 32, (e) => { return (function (e, t) { if (!i[e]) { i[e] = {}; for (let n = 0; n < e.length; n++)i[e][e.charAt(n)] = n } return i[e][t] }(r, t.charAt(e))) })) }, _compress(e, t, n) {
    if (e == null)
      return ''; let o; let r; let i; const a = {}; const s = {}; let c = ''; let f = ''; let u = ''; let d = 2; let l = 3; let p = 2; const h = []; let g = 0; let v = 0; for (i = 0; i < e.length; i += 1) {
      if (c = e.charAt(i), Object.prototype.hasOwnProperty.call(a, c) || (a[c] = l++, s[c] = !0), f = u + c, Object.prototype.hasOwnProperty.call(a, f)) {
        u = f
      }
      else {
        if (Object.prototype.hasOwnProperty.call(s, u)) {
          if (u.charCodeAt(0) < 256) { for (o = 0; o < p; o++)g <<= 1, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++; for (r = u.charCodeAt(0), o = 0; o < 8; o++)g = g << 1 | 1 & r, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++, r >>= 1 }
          else { for (r = 1, o = 0; o < p; o++)g = g << 1 | r, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++, r = 0; for (r = u.charCodeAt(0), o = 0; o < 16; o++)g = g << 1 | 1 & r, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++, r >>= 1 }--d == 0 && (d = 2 ** p, p++), delete s[u]
        }
        else {
          for (r = a[u], o = 0; o < p; o++)g = g << 1 | 1 & r, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++, r >>= 1
        }--d == 0 && (d = 2 ** p, p++), a[f] = l++, u = String(c)
      }
    } if (u !== '') {
      if (Object.prototype.hasOwnProperty.call(s, u)) {
        if (u.charCodeAt(0) < 256) { for (o = 0; o < p; o++)g <<= 1, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++; for (r = u.charCodeAt(0), o = 0; o < 8; o++)g = g << 1 | 1 & r, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++, r >>= 1 }
        else { for (r = 1, o = 0; o < p; o++)g = g << 1 | r, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++, r = 0; for (r = u.charCodeAt(0), o = 0; o < 16; o++)g = g << 1 | 1 & r, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++, r >>= 1 }--d == 0 && (d = 2 ** p, p++), delete s[u]
      }
      else {
        for (r = a[u], o = 0; o < p; o++)g = g << 1 | 1 & r, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++, r >>= 1
      }--d == 0 && (d = 2 ** p, p++)
    } for (r = 2, o = 0; o < p; o++)g = g << 1 | 1 & r, v == t - 1 ? (v = 0, h.push(n(g)), g = 0) : v++, r >>= 1; for (;;) { if (g <<= 1, v == t - 1) { h.push(n(g)); break }v++ } return h.join('')
  }, _decompress(e, t, n) {
    let o; let r; let i; let a; let s; let c; let f; const u = []; let d = 4; let l = 4; let p = 3; let h = ''; const g = []; const v = { val: n(0), position: t, index: 1 }; for (o = 0; o < 3; o += 1)u[o] = o; for (i = 0, s = 2 ** 2, c = 1; c != s;)a = v.val & v.position, v.position >>= 1, v.position == 0 && (v.position = t, v.val = n(v.index++)), i |= (a > 0 ? 1 : 0) * c, c <<= 1; switch (i) { case 0:for (i = 0, s = 2 ** 8, c = 1; c != s;)a = v.val & v.position, v.position >>= 1, v.position == 0 && (v.position = t, v.val = n(v.index++)), i |= (a > 0 ? 1 : 0) * c, c <<= 1; f = w(i); break; case 1:for (i = 0, s = 2 ** 16, c = 1; c != s;)a = v.val & v.position, v.position >>= 1, v.position == 0 && (v.position = t, v.val = n(v.index++)), i |= (a > 0 ? 1 : 0) * c, c <<= 1; f = w(i); break; case 2:return '' } for (r = u[3] = f, g.push(f); ;) {
      if (v.index > e)
        return ''; for (i = 0, s = 2 ** p, c = 1; c != s;)a = v.val & v.position, v.position >>= 1, v.position == 0 && (v.position = t, v.val = n(v.index++)), i |= (a > 0 ? 1 : 0) * c, c <<= 1; switch (f = i) { case 0:for (i = 0, s = 2 ** 8, c = 1; c != s;)a = v.val & v.position, v.position >>= 1, v.position == 0 && (v.position = t, v.val = n(v.index++)), i |= (a > 0 ? 1 : 0) * c, c <<= 1; u[l++] = w(i), f = l - 1, d--; break; case 1:for (i = 0, s = 2 ** 16, c = 1; c != s;)a = v.val & v.position, v.position >>= 1, v.position == 0 && (v.position = t, v.val = n(v.index++)), i |= (a > 0 ? 1 : 0) * c, c <<= 1; u[l++] = w(i), f = l - 1, d--; break; case 2:return g.join('') } if (d == 0 && (d = 2 ** p, p++), u[f]) {
        h = u[f]
      }
      else {
        if (f !== l)
          return null; h = r + r.charAt(0)
      }g.push(h), u[l++] = r + h.charAt(0), r = h, --d == 0 && (d = 2 ** p, p++)
    }
  } }), void 0 === (o = (function () { return window.LZString }.call(t, n, t, e))) || (e.exports = o)
}]))
