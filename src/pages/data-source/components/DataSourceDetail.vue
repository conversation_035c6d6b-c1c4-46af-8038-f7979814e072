<script lang="ts" setup>
import type { DataSourceCreate, DataSourceUpdate } from '~/services/data-source'
import { ElMessage } from 'element-plus'
import clonedeep from 'lodash.clonedeep'
import { useDict } from '~/composables/useDict'
import { dataSourceService } from '~/services/data-source'
import { FormType } from '~/types/common'
import { decrypt, encrypt } from '~/utils/crypto'
import { secretKey } from '../constants'

const emit = defineEmits<{
  (e: 'success'): void
}>()

const dicts = useDict(['databaseType'])
const baseDialogRef = ref() // 对话框引用
const formRef = ref() // 表单引用
const submitLoading = ref(false) // 表单提交加载状态
const formType = ref<FormType>(FormType.CREATE) // 表单类型：创建或编辑
const testLoading = ref(false) // 测试连接加载状态

// 秘钥
const databaseSecretKey = inject<ComputedRef<string>>(secretKey)

const defaultState: DataSourceCreate = {
  name: '',
  dbName: '',
  type: '',
  // catalog: '',
  url: '',
  username: '',
  password: '',
  config: '',
  remark: '',
}
// 表单数据
const formState = ref<DataSourceCreate | DataSourceUpdate>(clonedeep(defaultState))

// 表单验证规则
const rules = ref({
  name: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
  dbName: [{ required: true, message: '请输入数据库', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据库类型', trigger: 'change' }],
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  url: [{ required: true, message: '请输入URL', trigger: 'blur' }],
  // catalog: [{ required: true, message: '请输入catalog', trigger: 'change' }],
})

// 打开对话框
async function open(title: string, options: DataSourceUpdate | null) {
  formType.value = options?.id ? FormType.EDIT : FormType.CREATE
  formState.value = clonedeep(options || defaultState)
  formState.value.password = decrypt(formState.value.password, databaseSecretKey.value!)

  baseDialogRef.value.open({
    title,
  })

  await nextTick()
  await setTimeout(() => {}, 0)
  formRef.value.clearValidate()
}

// 测试连接
async function test({ showTip } = { showTip: false }) {
  try {
    testLoading.value = true
    await dataSourceService.test(getParams())
    if (showTip) {
      ElMessage.success('连接成功')
    }
    return true
  }
  catch (e) {
    console.error(e)
  }
  finally {
    testLoading.value = false
  }
}

// 获取表单参数
function getParams(): DataSourceCreate | DataSourceUpdate {
  const params = clonedeep(formState.value)
  params.password = encrypt(params.password, databaseSecretKey.value!)
  const { createAt, createBy, updateAt, updateBy, ...rest } = params
  return rest
}

function handleConfirm() {
  submitLoading.value = true
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      submitLoading.value = false
      return
    }
    try {
      // 测试链接
      if (!await test())
        return

      if (formType.value === FormType.CREATE) {
        await dataSourceService.create(getParams())
        ElMessage.success('创建成功')
      }
      else {
        await dataSourceService.update(getParams() as DataSourceUpdate)
        ElMessage.success('更新成功')
      }
      // 清除数据源列表缓存，确保其他组件能获取到最新数据
      dictCache.clearCache('dataSourceList')
      baseDialogRef.value.close()
      emit('success')
    }
    catch (e) {
      console.error(e)
    }
    finally {
      submitLoading.value = false
    }
  })
}

defineExpose({
  open,
})
</script>

<template>
  <BaseDialog ref="baseDialogRef" width="720px" :confirm-loading="submitLoading" @confirm="handleConfirm">
    <el-form ref="formRef" :model="formState" :rules="rules" label-width="100px">
      <el-form-item label="数据源名称" prop="name">
        <el-input v-model="formState.name" />
      </el-form-item>
      <el-form-item label="数据库类型" prop="type">
        <el-select v-model="formState.type" placeholder="请选择数据库类型">
          <el-option v-for="item in dicts.databaseType" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="URL" prop="url">
        <el-input v-model="formState.url" type="textarea" :rows="3" maxlength="200" show-word-limit />
      </el-form-item>
      <el-form-item label="数据库" prop="dbName">
        <el-input v-model="formState.dbName" />
      </el-form-item>
      <!--      <el-form-item label="Catalog" prop="catalog"> -->
      <!--        <el-input v-model="formState.catalog" /> -->
      <!--      </el-form-item> -->
      <el-form-item label="用户名" prop="username">
        <el-input v-model="formState.username" />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="formState.password" type="password" show-password />
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input v-model="formState.remark" type="textarea" :rows="3" maxlength="100" show-word-limit />
      </el-form-item>
    </el-form>
    <template #footer-before>
      <el-button type="primary" :loading="testLoading" plain @click="test({ showTip: true })">
        测试连接
      </el-button>
    </template>
  </BaseDialog>
</template>
