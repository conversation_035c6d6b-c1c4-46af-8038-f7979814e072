<script lang="ts" setup>
import type { DataSourceDetail } from '~/services/data-source'
import type { DescribeConfigOne } from '~/types/common'
import { usePagination } from '~/composables/pagination'
import { useDict } from '~/composables/useDict'
import { dataSourceService } from '~/services/data-source'
import { popUp } from '~/utils/popup'
import { secretKey } from './constants'

const dicts = useDict(['databaseType', 'databaseKey'])
const data = ref<DataSourceDetail[]>([])
const detailRef = ref()
const total = ref(0)
const loading = ref(false)
const formState = reactive({
  dsName: '',
  dbType: '',
})
provide(secretKey, computed(() => dicts?.databaseKey?.[0]?.value ?? ''))
const { pageNum, pageSize } = usePagination()

const descKeys: DescribeConfigOne[] = [
  { name: '数据库', key: 'name' },
  { name: '创建人', key: 'createBy' },
  { name: '创建时间', key: 'createAt' },
  { name: '描述', key: 'remark', tooltip: true },
]

onMounted(query)

function handleCreate() {
  detailRef.value?.open('新建数据源')
}

function handleEdit(one: DataSourceDetail) {
  detailRef.value?.open('编辑数据源', one)
}

async function handleDelete(one: DataSourceDetail) {
  try {
    const status = await popUp(`确定要删除数据源【${one.name}】吗？`)
    if (!status)
      return

    await dataSourceService.delete(one.id)
    query()
  }
  catch (error) {
    console.error(error)
  }
}

async function query({ initPage } = { initPage: false }) {
  try {
    loading.value = true
    if (initPage) {
      pageNum.value = 1
    }
    const { list, total: _total } = await dataSourceService.list({
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      page: '',
      ...formState,
    })
    data.value = list
    total.value = _total
  }
  catch (error) {
    console.error(error)
  }
  finally {
    loading.value = false
  }
}

function handleChangePagination() {
  query()
}
</script>

<template>
  <div class="h-full flex flex-col">
    <BaseHeader>
      <div class="flex items-center gap-2.5">
        <el-input v-model="formState.dsName" placeholder="请输入搜索关键字" class="min-w-62!" clearable @keyup.enter="query({ initPage: true })" @clear="query({ initPage: true })">
          <template #suffix>
            <i class="i-ph-magnifying-glass cursor-pointer" @click="query({ initPage: true })" />
          </template>
        </el-input>
        <el-select v-model="formState.dbType" class="min-w-52.5!" clearable @change="query({ initPage: true })">
          <el-option v-for="item in dicts.databaseType" :key="item.code" :value="item.code" :label="item.name" />
        </el-select>
        <el-button type="primary" @click="handleCreate">
          新建数据源
        </el-button>
      </div>
    </BaseHeader>
    <BaseContent :loading="loading">
      <BaseEmpty v-if="!data.length" />
      <div v-else overflow-x-hidden>
        <el-row :gutter="10">
          <el-col v-for="item in data" :key="item.id" :xl="6" :lg="8" :md="8" :xs="12" class="mb-2.5">
            <DescribeCard
              :data="item"
              :icon="`i-custom-${item.type}`"
              title-key="name"
              :desc-config="descKeys"
              @edit="handleEdit(item)"
              @delete="handleDelete(item)"
            />
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <FooterPagination
          v-model:page="pageNum"
          v-model:page-size="pageSize"
          :total="total"
          is-full-width
          @change="handleChangePagination"
        />
      </template>
    </BaseContent>
    <DataSourceDetail ref="detailRef" @success="query" />
  </div>
</template>
