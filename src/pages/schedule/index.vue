<script lang="ts" setup>
import { scheduleServices } from '~/services/schedule'

const tableRef = ref()
const otherParams = ref({})
const formState = ref({
  keyword: '',
})

const columns = ref([
  { label: '调度名称', prop: 'scheduleName', width: 200 },
  { label: '项目名称', prop: 'projectName' },
  { label: '规则集名称', prop: 'rulesetName' },
  { label: 'CRON表达式', prop: 'cronExpression' },
  { label: '状态', prop: 'statusName', slotName: 'statusName' },
  { label: '更新时间', prop: 'updateAt' },
  { label: '更新人', prop: 'updateByName' },
])

const getStatusName = (status: number) => status === 1 ? '运行中' : '已停止'

const actions = ref([
  {
    name: '启动',
    props: (scope: any) => ({
      disabled: scope.row.status === 1,
    }),
    handler: async ({ row }: { row: Record<string, any> }) => {
      try {
        await ElMessageBox.confirm('确定要启动该调度吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        await scheduleServices.start(row.id)
        ElMessage.success('启动成功')
        row.status = 1
        row.statusName = getStatusName(row.status)
      }
      catch {
        // User canceled the operation
      }
    },
  },
  {
    name: '停止',
    props: (scope: any) => ({
      disabled: scope.row.status === 0,
    }),
    handler: async ({ row }: { row: Record<string, any> }) => {
      try {
        await ElMessageBox.confirm('确定要停止该调度吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        await scheduleServices.stop(row.id)
        ElMessage.success('停止成功')
        row.status = 0
        row.statusName = getStatusName(row.status)
      }
      catch {
        // User canceled the operation
      }
    },
  },
  {
    name: '执行一次',
    props: () => ({}),
    handler: async ({ row }: { row: Record<string, any> }) => {
      try {
        await ElMessageBox.confirm('确定要执行一次该调度吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        await scheduleServices.run(row.id)
        ElMessage.success('任务开始执行')
      }
      catch {
        // User canceled the operation
      }
    },
  },
  {
    name: '删除',
    props: () => ({
      type: 'danger',
    }),
    handler: async ({ row }: { row: Record<string, any> }) => {
      try {
        await ElMessageBox.confirm('确定要删除该调度吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        await scheduleServices.delete(row.id)
        ElMessage.success('删除成功')
        tableRef.value.query()
      }
      catch {
        // User canceled the operation
      }
    },
  },
])

const api = reactive({
  fn: scheduleServices.list,
  getParams: () => ({
    ...otherParams.value,
    keyword: formState.value.keyword,
  }),
  resultFilter: (res: { list: any[] }) => {
    return res.list.map(item => ({
      ...item,
      statusName: getStatusName(item.status),
    }))
  },
})

function query(params?: Record<string, any>) {
  otherParams.value = params || otherParams.value
  tableRef.value.query({ initPage: true })
}

defineExpose({
  query,
})
</script>

<template>
  <div class="h-full flex flex-col">
    <BaseHeader>
      <div class="flex gap-2.5">
        <el-input v-model="formState.keyword" placeholder="请输入调度名称" class="min-w-62" clearable @keyup.enter="query({ initPage: true })" @clear="query({ initPage: true })">
          <template #suffix>
            <i class="i-ph-magnifying-glass cursor-pointer" @click="query({ initPage: true })" />
          </template>
        </el-input>
      </div>
    </BaseHeader>
    <BaseContent>
      <SearchTable ref="tableRef" :columns="columns" :api="api" :actions="actions">
        <template #statusName="{ row }">
          <div :class="{ 'color-green': row.status === 1 }">
            <i class="dot mr-2" :class="row.status === 1 ? 'bg-green' : ''" />{{ row.statusName }}
          </div>
        </template>
      </SearchTable>
    </BaseContent>
  </div>
</template>
