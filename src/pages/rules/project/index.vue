<script lang="ts" setup>
import type { ProjectDetail } from '~/services/project'
import type { DescribeConfigOne } from '~/types/common'
import { usePagination } from '~/composables/pagination'
import { projectService } from '~/services/project'
import { popUp } from '~/utils/popup'

const data = ref<ProjectDetail[]>([])
const detailRef = ref()
const total = ref(0)
const loading = ref(false)
const name = ref('')
const { pageNum, pageSize } = usePagination()
const projectKeys: DescribeConfigOne[] = [
  { name: '项目所属', key: 'ownerName' },
  { name: '项目成员', key: 'members', slotName: 'members' },
  { name: '创建时间', key: 'createTime' },
  { name: '修改时间', key: 'updateTime' },
  { name: '描述', key: 'description', tooltip: true },
]

onMounted(query)

function handleCreate() {
  detailRef.value?.open('新建项目')
}

function handleEdit(one: ProjectDetail) {
  detailRef.value?.open('编辑项目', one)
}

async function handleDelete(one: ProjectDetail) {
  try {
    const status = await popUp(`确定要删除项目【${one.name}】吗？`)
    if (!status)
      return

    await projectService.delete(one.id)
    query()
  }
  catch (error) {
    console.error(error)
  }
}

async function query({ initPage } = { initPage: false }) {
  try {
    loading.value = true
    if (initPage) {
      pageNum.value = 1
    }
    const { list, total: _total } = await projectService.list({
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      page: '',
      name: name.value,
    })
    data.value = list
    total.value = _total
  }
  catch (error) {
    console.error(error)
  }
  finally {
    loading.value = false
  }
}

function handleChangePagination() {
  query()
}
</script>

<template>
  <div class="h-full flex flex-col">
    <BaseHeader>
      <div>
        <el-input v-model="name" placeholder="请输入搜索关键字" class="w-62!" clearable @keyup.enter="query({ initPage: true })" @clear="query({ initPage: true })">
          <template #suffix>
            <i class="i-ph-magnifying-glass cursor-pointer" @click="query({ initPage: true })" />
          </template>
        </el-input>
        <el-button class="ml-2.5" type="primary" @click="handleCreate">
          新建项目
        </el-button>
      </div>
    </BaseHeader>
    <BaseContent :loading="loading">
      <div overflow-x-hidden>
        <el-row :gutter="10">
          <el-col v-for="item in data" :key="item.id" :xl="6" :lg="8" :md="8" :xs="12" class="mb-2.5">
            <DescribeCard
              :data="item"
              title-key="name"
              :desc-config="projectKeys"
              @edit="handleEdit(item)"
              @delete="handleDelete(item)"
            >
              <template #members="{ value }">
                <div class="overflow-hidden whitespace-nowrap" :title="value.map(m => m.userName).join(',')">
                  {{ value.slice(0, 3).map(m => m.userName).join('、') }}
                  <template v-if="value.length > 3">
                    等<span class="color-blue">{{ value.length }}</span>人
                  </template>
                </div>
              </template>
            </DescribeCard>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <FooterPagination
          v-model:page="pageNum"
          v-model:page-size="pageSize"
          :total="total"
          is-full-width
          @change="handleChangePagination"
        />
      </template>
    </BaseContent>
    <ProjectDetail ref="detailRef" @success="query" />
  </div>
</template>
