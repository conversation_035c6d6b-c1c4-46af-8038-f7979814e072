<script lang="ts" setup>
import type { ProjectCreate, ProjectUpdate } from '~/services/project'
import { ElMessage } from 'element-plus'
import clonedeep from 'lodash.clonedeep'
import { projectService } from '~/services/project'
import { FormType } from '~/types/common'

const emit = defineEmits<{
  (e: 'success'): void
}>()

const baseDialogRef = ref() // 对话框引用
const formRef = ref() // 表单引用
const submitLoading = ref(false) // 表单提交加载状态
const formType = ref<FormType>(FormType.CREATE) // 表单类型：创建或编辑

const defaultState = {
  name: '',
  description: '',
}
// 表单数据
const formState = ref<ProjectCreate | ProjectUpdate>(clonedeep(defaultState))

// 表单验证规则
const rules = ref({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' },
  ],
})

// 打开对话框
async function open(title: string, options: ProjectUpdate | null) {
  formType.value = options?.id ? FormType.EDIT : FormType.CREATE
  formState.value = clonedeep(options || defaultState)
  baseDialogRef.value.open({
    title,
  })

  await nextTick()
  await setTimeout(() => {}, 0)
  formRef.value.clearValidate()
}

function handleConfirm() {
  submitLoading.value = true
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      submitLoading.value = false
      return
    }
    try {
      if (formType.value === FormType.CREATE) {
        const { name, description } = formState.value
        const params = { name, description }
        await projectService.create(params)
        ElMessage.success('创建成功')
        // 清除项目列表缓存，确保其他组件能获取到最新数据
        dictCache.clearCache('projectList')
      }
      else {
        const { name, description, id } = formState.value as ProjectUpdate
        const params = { name, description, id }
        await projectService.update(params)
        ElMessage.success('更新成功')
        // 清除项目列表缓存，确保其他组件能获取到最新数据
        dictCache.clearCache('projectList')
      }
      baseDialogRef.value.close()
      emit('success')
    }
    catch (e) {
      console.error(e)
    }
    finally {
      submitLoading.value = false
    }
  })
}

defineExpose({
  open,
})
</script>

<template>
  <BaseDialog ref="baseDialogRef" width="720px" :confirm-loading="submitLoading" @confirm="handleConfirm">
    <el-form ref="formRef" :model="formState" :rules="rules">
      <el-form-item label="项目名称" prop="name">
        <el-input v-model="formState.name" />
      </el-form-item>
      <el-form-item label="项目描述" prop="description">
        <el-input v-model="formState.description" :autosize="{ minRows: 2, maxRows: 6 }" resize="none" type="textarea" />
      </el-form-item>
    </el-form>
  </BaseDialog>
</template>
