<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import BaseTable from '~/components/BaseTable/index.vue'
import CodeEditor from '~/components/CodeEditor.vue'
import { dataSetService } from '~/services/data-set'
import DataSetRunConfig from '../components/DataSetRunConfig.vue'

const router = useRouter()
const route = useRoute()
const id = Number((route.params as any).id)

const codeEditorRef = ref()
const runConfigRef = ref()

const activeName = ref('result') // active Tab
const queryStr = ref('') // 关键字
const state = reactive({
  title: '',
  code: '',
})
const showRunResult = ref(true) // 是否显示运行结果
const saveLoading = ref(false) // 保存加载
const runLoading = ref(false) // 运行加载
const columns = ref<any[]>([])
const runResultData = ref([]) // 运行结果数据
const tableData = ref([]) // 运行结果

onMounted(() => {
  query()
})
async function query() {
  try {
    const result = await dataSetService.detail(id)
    state.code = result.sqlText || ''
  }
  catch (e) {
    console.error(e)
  }
}

function filterResult() {
  if (!queryStr.value)
    return tableData.value = runResultData.value
  tableData.value = runResultData.value.filter((item: Record<string, any>) => {
    return Object.keys(item).some((key) => {
      return item[key]?.toString().includes(queryStr.value)
    })
  })
}

function handleBack() {
  router.push('/rules/data-set')
}

// 格式化
function handleFormat() {
  codeEditorRef.value?.format()
}

function handleRun() {
  runConfigRef.value?.open()
}
// 运行
async function doRun(params: { bizDate: string }) {
  try {
    runLoading.value = true
    const result = await dataSetService.run({ dataSetId: id, sql: state.code, ...params })
    if (result.length) {
      columns.value = Object.keys(result[0]).map(key => ({
        label: key,
        prop: key,
      }))
      runResultData.value = result
      tableData.value = result
    }
    runConfigRef.value?.close()
    showRunResult.value = true
  }
  catch (e) {
    console.error(e)
  }
  finally {
    runLoading.value = false
  }
}

// 保存
async function handleSave() {
  try {
    saveLoading.value = true
    await dataSetService.saveSql({ dataSetId: id, sql: state.code })
    ElMessage({
      type: 'success',
      message: '保存成功',
    })
  }
  catch (e) {
    console.error(e)
  }
  finally {
    saveLoading.value = false
  }
}
</script>

<template>
  <div class="h-full flex flex-col overflow-hidden">
    <div class="top-header left-0 top-0 w-full bg-white">
      <div class="text-base">
        数据集编辑
      </div>
      <div class="flex items-center">
        <el-button type="primary" plain @click="handleFormat">
          格式化
        </el-button>
        <el-button type="primary" plain>
          占位符管理
        </el-button>
        <el-button type="success" :disabled="!state.code" :loading="runLoading" @click="handleRun">
          运行
        </el-button>
        <el-divider direction="vertical" />
        <el-button @click="handleBack">
          关闭
        </el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSave">
          保存
        </el-button>
      </div>
    </div>
    <div class="h-full flex flex-1 flex-col overflow-hidden overflow-hidden px-4 pt-5">
      <CodeEditor ref="codeEditorRef" v-model="state.code" style="min-height: 30vh;" class="flex-1" />
      <div
        class="flex-0 max-h-[calc(100vh-30vh-62px)] flex flex-col overflow-hidden transition-200 transition-all" :style="{ height: showRunResult ? 'unset' : '54px' }"
        :class="{ 'min-h-120': showRunResult }"
      >
        <div class="flex items-center justify-between">
          <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="运行结果" name="result" />
          </el-tabs>
          <div class="flex items-center gap-2.5">
            <el-input v-model="queryStr" placeholder="请输入搜索关键字" class="w-62!" clearable @clear="filterResult">
              <template #suffix>
                <i class="i-ph-magnifying-glass cursor-pointer" @click="filterResult" />
              </template>
            </el-input>

            <div class="cursor-pointer rounded-md p-0.5 line-height-0 hover:bg-gray-2">
              <i :class="showRunResult ? 'i-ph-caret-down' : 'i-ph-caret-up'" @click="showRunResult = !showRunResult" />
            </div>
          </div>
        </div>
        <div class="flex-1 overflow-hidden">
          <BaseTable style="height: 100%;" :columns="columns" :loading="runLoading" :data="tableData" :show-page="false" />
        <!-- <SearchTable :columns="columns" /> -->
        </div>
      </div>
    </div>
    <DataSetRunConfig ref="runConfigRef" :confirm-loading="runLoading" @confirm="doRun" />
  </div>
</template>

<style lang="scss" scoped>
.top-header {
  box-shadow: 0px 2px 8px 0px rgba(55, 74, 76, 0.15);
  @apply h-full w-full overflow-hidden h-15.5 py-4 pr-5 pl-7.5 flex justify-between items-center;
}
</style>

<route lang="yaml">
  meta:
    title: "数据集详情"
    fullScreen: "1"
</route>
