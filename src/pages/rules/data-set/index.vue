<script lang="ts" setup>
import { ROOT_NODE_ID } from '~/enums/tree'
import { isFolder } from '~/utils/tree'

const router = useRouter()
const dataSetOverviewRef = ref()
const sideRef = ref()
// 当前选择
const chooseId = ref<number | null>(null)
const chooseIsFolder = ref(false)

// 拖拽调整宽度相关
const sideMenuWidth = ref(230) // 默认宽度230px
const isDragging = ref(false)
const minWidth = 200 // 最小宽度
const maxWidth = 500 // 最大宽度

const baseFormRef = ref()
async function handleCurrentChange(data: any) {
  // 清空
  if (!data)
    return chooseId.value = null
  chooseId.value = data.id
  chooseIsFolder.value = isFolder(data)
  await nextTick()
  dataSetOverviewRef.value?.init(data.id)
}

function handleCreate() {
  baseFormRef.value.open('新建数据集', { groupId: chooseIsFolder.value ? chooseId.value : ROOT_NODE_ID })
}
function handleEditBaseInfo(data: any) {
  baseFormRef.value.open('编辑数据集', data)
}
function handleEdit(data?: any) {
  const id = data?.id || chooseId.value
  router.push(`/rules/data-set/edit/${id}`)
}

async function handleEditSuccess() {
  if (!chooseId.value)
    return
  await nextTick()
  dataSetOverviewRef.value?.init(chooseId.value)
}

function handleCreateSuccess(params: any) {
  sideRef.value?.refresh(params.groupId)
}

// 拖拽相关方法
let startX = 0
let startWidth = 0

function handleMouseDown(e: MouseEvent) {
  isDragging.value = true
  startX = e.clientX
  startWidth = sideMenuWidth.value
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

function handleMouseMove(e: MouseEvent) {
  if (!isDragging.value)
    return

  const deltaX = e.clientX - startX
  const newWidth = startWidth + deltaX

  if (newWidth >= minWidth && newWidth <= maxWidth) {
    sideMenuWidth.value = newWidth
  }
}

function handleMouseUp() {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}
</script>

<template>
  <div class="h-full flex gap-0 overflow-hidden pl-4 pt-4">
    <div
      class="h-full flex-shrink-0 bg-white pt-3"
      :style="{ width: `${sideMenuWidth}px` }"
    >
      <DataSetSideMenu ref="sideRef" @current-change="handleCurrentChange" @create="handleCreate" />
    </div>

    <!-- 拖拽手柄 -->
    <div
      class="group h-full w-2 flex cursor-col-resize items-start justify-center bg-gray-100 pt-3 transition-colors duration-200 hover:bg-gray-200"
      @mousedown="handleMouseDown"
    >
      <div class="h-20 w-1 rounded-full bg-gray-300 transition-colors duration-200 group-hover:bg-gray-400" />
    </div>

    <div class="flex-1 overflow-hidden pl-2 pr-4">
      <DataSetList v-if="!chooseId" @edit="handleEdit" @view-detail="handleCurrentChange" />
      <DataSetOverview v-else ref="dataSetOverviewRef" :data-id="chooseId" @edit-base="handleEditBaseInfo" @edit-sql="handleEdit" />
    </div>
    <DataSetBaseForm ref="baseFormRef" @edit-success="handleEditSuccess" @create-success="handleCreateSuccess" />
  </div>
</template>
