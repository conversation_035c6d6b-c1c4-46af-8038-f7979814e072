<script lang="ts" setup>
import { getStateFromFields } from '~/components/DynamicForm'

const emit = defineEmits<{
  (e: 'confirm', model: { bizDate: string }): void
}>()

const formRef = ref()
const fields = ref({
  bizDate: {
    type: 'date',
    label: '业务日期',
    required: true,
  },
})
const model = ref(getStateFromFields(fields.value))
const visible = ref(false)

function open() {
  visible.value = true
}

async function handleConfirm() {
  const result = await formRef.value?.validate()
  if (!result)
    return
  emit('confirm', model.value)
}

function close() {
  visible.value = false
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <BaseDialog v-model="visible" v-bind="$attrs" width="480px" @cancel="close" @confirm="handleConfirm">
    <DynamicForm ref="formRef" v-model="model" :fields="fields" :use-grid="false" />
  </BaseDialog>
</template>
