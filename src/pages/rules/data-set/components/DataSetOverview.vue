<script lang="ts" setup>
import type { DataSetDetail } from '~/services/data-set'
import { dataSetService } from '~/services/data-set'

const emit = defineEmits<{
  (e: 'editBase', params: any): void
  (e: 'editSql', params: any): void
}>()

const currentId = ref()

const data = ref<DataSetDetail | null>(null)
const loading = ref(false)
const activeName = ref('1')
const formState = reactive({
  name: '',
})
const list = ref<DataSetDetail['dataSetMetaList']>([])

const desc = ref<{ title: string, key: keyof DataSetDetail }[]>([
  { title: '所属项目', key: 'projectName' },
  { title: '数据源', key: 'dataSourceName' },
  { title: '描述', key: 'description' },
])
const columns = ref([
  {
    label: '字段名',
    prop: 'columnName',
  },
  {
    label: '别名',
    prop: 'columnAlias',
  },
  {
    label: '字段类型',
    prop: 'dataType',
    optionKey: 'dataType',
  },
])

async function queryDetail() {
  try {
    loading.value = true
    const result = await dataSetService.detail(currentId.value)
    data.value = result
    list.value = result.dataSetMetaList || []
  }
  catch (error) {
    console.error(error)
  }
  finally {
    loading.value = false
  }
}

function filterStr(queryStr?: string) {
  if (!queryStr)
    return list.value = data.value?.dataSetMetaList || []
  list.value = (data.value?.dataSetMetaList || [])
    .filter(item => item.columnAlias.includes(queryStr!) || item.columnName.includes(queryStr!))
}

function handleClickTab(tab: any) {
  console.error(tab)
}

async function init(id: number) {
  currentId.value = id
  await queryDetail()
}

defineExpose({
  init,
})
</script>

<template>
  <div v-loading="loading" class="h-full flex flex-col gap-3">
    <div v-if="data" class="flex bg-white">
      <div class="h-20 flex flex-[0_0_80px] flex-col items-center justify-center bg-primary">
        <i class="i-custom-list color-white fs-40" />
        <span class="mt-1 text-sm color-white font-200">数据集</span>
      </div>
      <div class="flex flex-1 items-center justify-between pb-3 pl-5 pr-2.5 pt-2">
        <div class="h-full flex flex-col justify-between">
          <h4 class="font-500">
            {{ data.name }}
          </h4>
          <div class="flex items-center">
            <ul class="flex items-center gap-3 text-xs">
              <li v-for="one in desc" :key="one.key">
                <span class="color-desc">{{ one.title }}：</span>{{ data[one.key] }}
              </li>
            </ul>
            <i class="i-ph-note-pencil ml-2 cursor-pointer color-desc" @click="emit('editBase', data)" />
          </div>
        </div>
        <el-button type="primary" @click="emit('editSql', data)">
          编辑
        </el-button>
      </div>
    </div>
    <div flex flex-1 flex-col overflow-hidden>
      <div flex items-center justify-between>
        <el-tabs v-model="activeName" class="custom-tabs" @tab-click="handleClickTab">
          <el-tab-pane label="模型信息" name="1" />
        </el-tabs>
        <el-input v-model="formState.name" placeholder="请输入搜索关键字" class="w-62!" clearable @clear="filterStr">
          <template #suffix>
            <i class="i-ph-magnifying-glass cursor-pointer" @click="filterStr(formState.name)" />
          </template>
        </el-input>
      </div>
      <div class="flex-1 overflow-hidden bg-white">
        <BaseTable :columns="columns" :data="list || []" :page="false" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.custom-tabs.el-tabs {
  :deep(.el-tabs__nav-wrap::after) {
    background-color: unset !important;
  }
}
</style>
