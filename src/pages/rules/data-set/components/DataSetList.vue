<script lang="ts" setup>
import type { DataSetDetail } from '~/services/data-set'
import { getStateFromFields } from '~/components/DynamicForm'
import { dataSetService } from '~/services/data-set'

const emit = defineEmits<{
  (e: 'edit', data: DataSetDetail): void
  (e: 'viewDetail', data: DataSetDetail): void
}>()

const tableRef = ref()
const fields = ref({
  dataSetName: {
    type: 'input' as const,
    label: '数据集名称',
  },
  projectName: {
    type: 'input' as const,
    label: '项目名称',
  },
  dataSourceId: {
    type: 'asyncSelect' as const,
    label: '数据源',
    bindings: {
      optionKey: 'dataSourceList',
      immediate: true,
    },
  },
})
const model = ref(getStateFromFields(fields.value))

const columns = ref([
  {
    prop: 'name',
    label: '数据集名称',
    minWidth: 220,
    slotName: 'name',
  },
  {
    prop: 'projectName',
    label: '项目名称',
    minWidth: 120,
  },
  {
    prop: 'dataSourceName',
    label: '数据源',
    minWidth: 180,
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: 180,
  },
  {
    prop: 'createByName',
    label: '创建人',
    minWidth: 80,
  },
  {
    prop: 'updateTime',
    label: '修改时间',
    minWidth: 180,
  },
  {
    prop: 'updateByName',
    label: '修改人',
    minWidth: 80,
  },
])

const actions = ref([
  {
    name: '编辑',
    handler: ({ row }: { row: DataSetDetail }) => {
      emit('edit', row)
    },
  },
  {
    name: '删除',
    handler: ({ row }: { row: DataSetDetail }) => {
      dataSetService.delete(row.id).then(() => tableRef.value?.query())
    },
  },
])

const api = reactive({
  fn: dataSetService.list,
  getParams: () => ({
    ...model.value,
  }),
})

function handleQuery() {
  tableRef.value?.query({ initPage: true })
}

function handleViewDetail(row: DataSetDetail) {
  emit('viewDetail', row)
}
</script>

<template>
  <div class="h-full flex flex-1 flex-col gap-2.5">
    <h3>概览</h3>

    <SearchForm v-model="model" :fields="fields" @submit="handleQuery" />
    <div class="flex-1 bg-white">
      <SearchTable ref="tableRef" :columns="columns" :actions="actions" :api="api">
        <template #name="{ row }">
          <span
            class="cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
            @click="handleViewDetail(row)"
          >
            {{ row.name }}
          </span>
        </template>
      </SearchTable>
    </div>
  </div>
</template>
