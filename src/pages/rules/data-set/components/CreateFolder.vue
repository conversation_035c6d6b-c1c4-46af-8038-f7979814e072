<script lang="ts" setup>
import { getStateFromFields } from '~/components/DynamicForm'
import DynamicForm from '~/components/DynamicForm/DynamicForm.vue'

defineProps<{
  submitLoading?: boolean
}>()
const emit = defineEmits<{
  (e: 'submit', data: any): void
}>()

const { closePopover } = useGlobalPopover()

const formRef = ref()
const fields = ref({
  parentId: {
    required: true,
    type: 'treeSelect',
    bindings: {
      optionKey: 'dataSetGroup',
      teleported: false,
    },
  },
  name: {
    type: 'input',
    required: true,
    bindings: {
      placeholder: '请输入文件夹名称',
    },
  },
})
const model = ref(getStateFromFields(fields.value))

async function submit() {
  const result = await formRef.value.validate()
  if (!result)
    return
  emit('submit', model.value)
}
</script>

<template>
  <div class="w-40">
    <DynamicForm ref="formRef" v-model="model" :fields="fields" :use-grid="false" />
    <div class="flex justify-end">
      <el-button @click="closePopover">
        取消
      </el-button>
      <el-button type="primary" :loading="submitLoading" @click="submit">
        确定
      </el-button>
    </div>
  </div>
</template>
