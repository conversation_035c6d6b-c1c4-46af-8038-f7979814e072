<script lang="ts" setup>
import type Node from 'element-plus/es/components/tree/src/model/node'
import type { AllowDropType, NodeDropType } from 'element-plus/es/components/tree/src/tree.type.mjs'
import { ElMessage } from 'element-plus'
import ConfirmDelete from '~/components/ConfirmDelete.vue'
import { ROOT_NODE_ID } from '~/enums/tree'
import { dataSetService } from '~/services/data-set'
import { isFolder } from '~/utils/tree'

interface Tree {
  name: string
  leaf?: boolean
}

const { openPopover, closePopover } = useGlobalPopover()

const treeRef = ref()
const loading = ref(false)
const currentNode = ref()

// 根节点状态，用于刷新节点数据
const rootState = reactive<any>({
  node: null,
  resolve: null,
})
async function loadNode(node: Node, resolve: (data: Tree[]) => void) {
  if (node.level === 0) {
    loading.value = true
    rootState.node = node
    rootState.resolve = resolve
    const res = await query({ parentId: ROOT_NODE_ID })
    loading.value = false
    resolve(res)
    return
  }
  resolve(await query({ parentId: node.data!.id }))
}

async function query({ parentId = 0 }) {
  try {
    const res = await dataSetService.tree({ parentId })
    return (res || []).map(item => ({ ...item, leaf: item.nodeType !== '1' }))
  }
  catch (e) {
    console.error(e)
    return []
  }
}

async function handleDelete(e: Event, node) {
  currentNode.value = node
  openPopover(ConfirmDelete, e, {
    bindings: {
      onConfirm: doDelete,
    },
  })
}
async function doDelete() {
  const node = currentNode.value
  try {
    if (isFolder(node.data)) {
      await dataSetService.deleteGroup(node.data.id)
      dictCache.clearCache('dataSetGroup')
    }
    else {
      await dataSetService.delete(node.data.id)
    }
    closePopover()
    ElMessage.success('删除成功')
    refresh(node.data.parentId)
    currentNode.value = null
  }
  catch (e) {
    console.error(e)
  }
}

// 刷新节点数据，id 为 ROOT_NODE_ID 时刷新根节点
async function refresh(id: string | number) {
  if (id === ROOT_NODE_ID) {
    getExpandedIds()
    rootState.node.childNodes = []
    loadNode(rootState.node, rootState.resolve)
  }
  else {
    const node = treeRef.value.treeRef.getNode(id)
    if (node && node.loaded) {
      node.loaded = false
      node.expand()
    }
  }
}

// 获取展开的节点id数组
function getExpandedIds(): Array<string | number> {
  const ids: Array<string | number> = []
  const nodesMap = treeRef.value.treeRef.root.store.nodesMap
  Object.keys(nodesMap).forEach((key) => {
    if (nodesMap[key].expanded) {
      ids.push(nodesMap[key].data.id)
    }
  })
  return ids as Array<string | number>
}

function setCurrentKey(key: string | number | null) {
  treeRef.value.treeRef.setCurrentKey(key)
}

// 是否允许拖拽到某个节点
function allowDrop(draggingNode: Node, dropNode: Node, type: AllowDropType) {
  const draggingIsFile = !isFolder(draggingNode.data as any)
  const dropIsFile = !isFolder(dropNode.data as any)

  const draggingParentId = (draggingNode.parent as any).data.parentId
  const dropParentId = (dropNode.parent as any).data.parentId

  // 文件只能拖到文件夹里
  if (dropIsFile && type === 'inner')
    return false

  // 同级文件不允许拖拽
  if (draggingIsFile && dropIsFile && draggingParentId === dropParentId)
    return false
  // 其他情况都允许
  return true
}

async function handleDragEnd(draggingNode: Node, dropNode: Node, dropType: NodeDropType) {
  if (dropType === 'none')
    return

  let targetGroupId
  if (dropType === 'inner') {
    targetGroupId = dropNode.data.id
  }
  else {
    targetGroupId = (dropNode.data as any).parentId
  }

  const _isFolder = isFolder(draggingNode.data as any)
  const api = _isFolder ? dataSetService.moveGroupToGroup : dataSetService.moveDataSetToGroup
  try {
    await api({
      [_isFolder ? 'groupId' : 'dataSetId']: draggingNode.data.id,
      targetGroupId,
    } as any)
    ElMessage.success('操作成功')
  }
  catch (e) {
    console.error(e)
  }
}

defineExpose({
  refresh,
  setCurrentKey,
})
</script>

<template>
  <BaseTree
    ref="treeRef"
    v-loading="loading"
    file-icon="i-ph-grid-nine"
    folder-icon="i-ph-folder-simple"
    v-bind="$attrs"

    lazy draggable highlight-current
    :expand-on-click-node="false"
    :props="{ label: 'name', children: 'children', isLeaf: 'leaf' }"
    :load="loadNode"
    :allow-drop="allowDrop"
    @node-drag-end="handleDragEnd"
  >
    <template #actions="{ node }">
      <i class="i-ph-trash" @click="(e) => handleDelete(e, node)" />
    </template>
  </BaseTree>
</template>
