<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { useGlobalPopover } from '~/composables/useGlobalPopover'
import { dataSetService } from '~/services/data-set'
import { isFolder } from '~/utils/tree'
import CreateFolder from './CreateFolder.vue'

const emit = defineEmits<{
  (e: 'currentChange', data: any, node: any): void
  (e: 'create'): void
  (e: 'createFolderSuccess', data: any): void
}>()

const { openPopover, closePopover } = useGlobalPopover()

const treeRef = ref()
const submitLoading = ref(false)
const isListActive = ref(true)

function handleToList() {
  treeRef.value.setCurrentKey(null)
}

// 新建文件夹
function handleCreateFolder(e: Event) {
  openPopover(CreateFolder, e, {
    bindings: {
      submitLoading,
      onSubmit: async (data: any) => {
        try {
          submitLoading.value = true
          await dataSetService.createGroup(data)
          closePopover()
          ElMessage.success('创建成功')
          emit('createFolderSuccess', data)
          refresh(data.parentId)
          dictCache.clearCache('dataSetGroup')
        }
        catch (e) {
          console.error(e)
        }
        finally {
          submitLoading.value = false
        }
      },
    },
  })
}

function handleCurrentChange(data: any, node: any) {
  isListActive.value = !data
  if (data && isFolder(data))
    return
  emit('currentChange', data, node)
}

function refresh(id: number | string) {
  treeRef.value?.refresh(id)
}

defineExpose({
  refresh,
})
</script>

<template>
  <div class="h-full w-full">
    <header mx-2.5 flex justify-between>
      <h3>数据集</h3>
      <div flex items-center gap-2>
        <div :class="{ 'bg-primary-lighter': isListActive }" class="cursor-pointer p-0.5 line-height-0">
          <i class="i-custom-list" :class="{ 'color-primary': isListActive }" @click="handleToList" />
        </div>
        <i class="i-ph-folder-simple" @click="handleCreateFolder" />
        <!-- <i class="i-ph-plus-circle" /> -->
      </div>
    </header>
    <div mt-2.5 px-2.5>
      <el-button w-full @click="emit('create')">
        新建数据集
      </el-button>
    </div>
    <el-divider border-style="dotted" class="my-2.5!" />
    <div px-2.5>
      <DataSetTree ref="treeRef" node-key="id" @current-change="handleCurrentChange" />
    </div>
  </div>
</template>
