<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import BaseDialog from '~/components/BaseDialog.vue'
import { getStateFromFields } from '~/components/DynamicForm'
import DynamicForm from '~/components/DynamicForm/DynamicForm.vue'
import { dataSetService } from '~/services/data-set'

const emit = defineEmits<{
  (event: 'editSuccess', params: any): void
  (event: 'createSuccess', params: any): void
}>()
const visible = ref(false)
const submitLoading = ref(false)
const isEdit = ref(false)
const dialogTitle = ref('')
const formRef = ref()

const fields = ref({
  name: {
    label: '数据集名称',
    type: 'input' as const,
    required: true,
  },
  groupId: {
    label: '分组',
    required: true,
    type: 'treeSelect' as const,
    bindings: {
      optionKey: 'dataSetGroup',
    },
  },
  projectId: {
    label: '项目',
    required: true,
    type: 'select2' as const,
    bindings: {
      optionKey: 'projectList',
    },
  },
  dataSourceId: {
    label: '数据源',
    required: true,
    type: 'select2' as const,
    bindings: {
      optionKey: 'dataSourceList',
    },
  },
  description: {
    label: '描述',
    type: 'input' as const,
    bindings: {
      type: 'textarea',
    },
  },
})
const model = ref<any>(getStateFromFields(fields.value))

async function open(title: string, params: any) {
  dialogTitle.value = title
  isEdit.value = !!params?.id
  if (isEdit.value)
    model.value = await query(params.id)
  else
    model.value = { ...getStateFromFields(fields.value), ...(params || {}) }
  visible.value = true

  await nextTick()
  await setTimeout(() => {}, 0)
  formRef.value.clearValidate()
}
function close() {
  visible.value = false
}

async function query(id: number) {
  try {
    const result = await dataSetService.detail(id)
    return result
  }
  catch (e) {
    console.error(e)
    return null
  }
}

async function handleSubmit() {
  const result = await formRef.value.validate()
  if (!result)
    return
  submitLoading.value = true
  try {
    if (isEdit.value) {
      const { createBy, createByName, delFlag, sqlText, updateBy, updateByName, updateTime, ...rest } = model.value
      const params = { ...rest }
      await dataSetService.update(params)
      ElMessage.success('编辑成功')
      emit('editSuccess', params)
    }
    else {
      await dataSetService.create(model.value)
      ElMessage.success('新增成功')
      emit('createSuccess', model.value)
    }
    // 手动刷新项目列表
    await dictCache.clearCache('projectList') // 清除项目列表缓存
    close()
  }
  catch (e) {
    console.error(e)
  }
  finally {
    submitLoading.value = false
  }
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <BaseDialog v-model="visible" :title="dialogTitle" :confirm-loading="submitLoading" @cancel="close" @confirm="handleSubmit">
    <DynamicForm ref="formRef" v-model="model" :fields="fields" :use-grid="false" label-width="100px" />
  </BaseDialog>
</template>
