<script lang="ts" setup>
import { ruleServices } from '~/services/rule'

const id = +useRoute().params.id as number
const router = useRouter()

const tableRef = ref()
const rulesRunRef = ref()

const otherParams = ref({})
const columns = ref([
  { label: '规则名称', prop: 'ruleName' },
  { label: '运行状态', prop: 'trialResult', type: 'tag', tagColors: {
    0: 'info',
    1: 'success',
    2: 'danger',
    3: 'warning',
  }, optionKey: 'runStatus', width: 120 },
  { label: '生效状态', prop: 'enable', type: 'switch', width: 100 },
  { label: '规则类型', prop: 'ruleType', optionKey: 'ruleType' },
  { label: '规则模版', prop: 'templateName' },
  { label: '规则强度', prop: 'ruleStrength', optionKey: 'ruleStrength', width: 100 },
  { label: '调度类型', prop: '', slotName: 'scheduleType' },
])

const actions = ref([
  {
    name: '运行',
    handler: handleRunRule,
  },
  {
    name: '编辑',
    handler: ({ row }: { row: any }) => {
      router.push(`/rules/rule-set/rule-config/edit/${row.id}`)
    },
  },
  {
    name: '删除',
    handler: async ({ row }: { row: any }) => {
      try {
        await ruleServices.delete(row.id)
        ElMessage.success('删除成功')
        query()
      }
      catch (e) {
        console.error(e)
      }
    },
  },
])

const api = reactive({
  fn: ruleServices.list,
  getParams: () => ({
    rulesetId: id,
    ...otherParams.value,
  }),
})

function query(params?: Record<string, any>) {
  otherParams.value = params || otherParams.value
  tableRef.value.query({ initPage: true })
}

function handleTableChange(type: string, params: any) {
  const { column, row } = params
  if (type === 'form-item' && column.property === 'enable') {
    doSwitch(row)
  }
}

function handleRunRule({ row }: { row: any }) {
  rulesRunRef.value.open(row)
}

async function doSwitch(row: any) {
  try {
    await ruleServices.switch({ id: row.id, valid: row.enable })
    ElMessage.success('操作成功')
    query()
  }
  catch (error) {
    console.error(error)
    row.enable = !row.enable
  }
}

defineExpose({
  query,
})
</script>

<template>
  <SearchTable ref="tableRef" :columns="columns" :api="api" :actions="actions" @change="handleTableChange">
    <template #scheduleType="{ row }">
      <el-popover
        v-if="row.ruleScheduleLinks && row.ruleScheduleLinks.length > 0"
        placement="left"
        title="调度详情"
        :width="200"
        trigger="click"
      >
        <template #reference>
          <div>
            <div>{{ row.ruleScheduleLinks.map(i => i.scheduleName).join(',') }}</div>
            <div class="cursor-pointer color-primary">
              调度详情
            </div>
          </div>
        </template>
        <div class="border-b-1 border-t-1 border-color-slate-200 border-solid py-3">
          <span v-for="item in row.ruleScheduleLinks" :key="item.scheduleId">{{ item.scheduleName }}</span>
        </div>
      </el-popover>
    </template>
  </SearchTable>
  <RulesRun ref="rulesRunRef" @success="query" />
</template>
