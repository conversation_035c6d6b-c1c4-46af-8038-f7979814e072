<script lang="ts" setup>
import type { RuleDetailModule } from '~/types/rules'
import BaseContent from '~/components/BaseContent.vue'
import { ruleFormConfigKey } from '~/enums/provide'
import { ruleServices } from '~/services/rule'
import { ruleStateKey } from '../../../../enums/provide'

provide(ruleFormConfigKey, {
  labelWidth: '100px',
  labelPosition: 'right',
})

const router = useRouter()
const route = useRoute()
const currentRuleId = route.params.id ? +(route.params.id as string) : 0

const data = ref()
const parentRefMap = reactive(new Map())
const submitLoading = ref(false)

const isEdit = computed(() => !!currentRuleId)

provide(ruleStateKey, {
  getParentRef,
  data,
  isEdit,
})

onMounted(() => {
  if (isEdit.value)
    query()
})

function getParentRef(key: RuleDetailModule): any {
  return parentRefMap.get(key)
}
function setParentRef(key: string, ref: any) {
  parentRefMap.set(key, ref)
}

// 查询详情数据 (编辑时使用)
async function query() {
  try {
    const result = await ruleServices.query(currentRuleId)
    data.value = result
    initModules()
  }
  catch (error) {
    console.error(error)
  }
}

// 初始化所有模块数据
function initModules() {
  Array.from(parentRefMap.values()).forEach(item => item.init?.())
}

// 获取提交参数
function getParams() {
  return Array.from(parentRefMap.values()).reduce((pre, cur) => {
    return {
      ...pre,
      ...cur.getData(),
    }
  }, {
    id: isEdit.value ? currentRuleId : undefined,
    rulesetId: useRulesStore().currentRuleSetId,
  })
}

// 校验所有模块
async function validateAll() {
  const validators = await Promise.all(Array.from(parentRefMap.values()).map(item => item.validate()))
  return validators.every(item => item)
}

// 提交表单
async function handleSubmit() {
  submitLoading.value = true
  if (!await validateAll())
    return submitLoading.value = false

  try {
    const params = getParams()
    if (!isEdit.value) {
      await ruleServices.create(params)
      ElMessage.success('新建规则成功')
    }
    else {
      await ruleServices.update(params)
      ElMessage.success('更新规则成功')
    }

    router.back()
  }
  catch (error) {
    console.error(error)
  }
  finally {
    submitLoading.value = false
  }
}

function handleBack() {
  router.back()
}
</script>

<template>
  <div class="rule-detail">
    <BaseHeader />
    <BaseContent>
      <div class="rule-detail-container">
        <div class="bg-white pl-7.5">
          <BaseTitle>规则基本信息</BaseTitle>
          <RulesBaseInfo :ref="(ref) => setParentRef('baseInfo', ref)" />
          <BaseTitle>监控对象</BaseTitle>
          <RulesMonitor :ref="(ref) => setParentRef('monitor', ref)" />
          <BaseTitle>规则配置</BaseTitle>
          <RulesConfig :ref="(ref) => setParentRef('ruleConfig', ref)" />
          <BaseTitle>调度配置</BaseTitle>
          <RulesDispatch :ref="(ref) => setParentRef('dispatch', ref)" />
          <BaseTitle>Mantis配置</BaseTitle>
          <RulesMantis :ref="(ref: any) => setParentRef('mantis', ref)" />
          <BaseTitle>其他配置</BaseTitle>
          <RulesOther :ref="(ref) => setParentRef('other', ref)" />
        </div>
        <div class="flex justify-end border-t-1 border-color-#f1f1f1 border-t-solid bg-white p-4">
          <el-button @click="handleBack">
            取消
          </el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确认
          </el-button>
        </div>
      </div>
    </BaseContent>
  </div>
</template>

<style lang="scss" scoped>
.rule-detail {
  @apply h-full overflow-hidden flex flex-col;
  :deep(.el-input),
  :deep(.el-select) {
    @apply max-w-140;
  }
  :deep(.el-textarea) {
    @apply max-w-180;
  }
}

.rule-detail-container {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}
</style>
