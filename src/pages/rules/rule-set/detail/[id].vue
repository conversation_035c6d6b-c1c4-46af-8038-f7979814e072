<script lang="ts" setup>
import type { RuleSetDetail } from '~/services/rule-set'
import { ruleSetServices } from '~/services/rule-set'
import RuleList from '../rule-config/list.component.vue'
import ScheduleList from '../schedule/list.component.vue'

const id = +useRoute().params.id as number
const router = useRouter()
const rulesStore = useRulesStore()

const detailRef = ref()
const data = ref<RuleSetDetail>({})
const loading = ref(false)
const activeName = ref('1')
const formStateOfRule = reactive({
  ruleName: '',
})
const ruleListRef = ref() // 规则列表组件引用
const scheduleListRef = ref() // 调度列表组件引用
const warningListRef = ref() // 告警列表组件引用

const desc = ref<{ title: string, key: keyof RuleSetDetail }[]>([
  { title: '所属项目', key: 'projectName' },
  { title: '描述', key: 'remark' },
])

onMounted(() => {
  rulesStore.currentRuleSetId = id
  query()
})

function handleEdit() {
  detailRef.value?.open('修改规则集', data.value)
}

async function query() {
  try {
    loading.value = true
    const result = await ruleSetServices.detail(id)
    data.value = result
  }
  catch (error) {
    console.error(error)
  }
  finally {
    loading.value = false
  }
}

function handleRuleQuery() {
  ruleListRef.value?.query(formStateOfRule)
}

function handleCreateRule() {
  router.push('/rules/rule-set/rule-config/create')
}

function handleCreateSchedule() {
  scheduleListRef.value?.create()
}
function handleCreateWarning() {
  warningListRef.value?.create()
}
</script>

<template>
  <div v-loading="loading" class="h-full flex flex-col">
    <BaseHeader />
    <BaseContent>
      <div class="h-full flex flex-col overflow-hidden">
        <div class="flex bg-white">
          <div class="h-20 flex flex-[0_0_80px] flex-col items-center justify-center bg-primary">
            <i class="i-custom-list color-white fs-40" />
            <span class="mt-1 text-sm color-white font-200">规则集</span>
          </div>
          <div class="flex flex-1 items-center justify-between pb-3 pl-5 pr-2.5 pt-2">
            <div class="h-full flex flex-col justify-between">
              <h4 class="font-500">
                {{ data.name }}
              </h4>
              <div class="flex items-center">
                <ul class="flex items-center gap-3 text-xs">
                  <li v-for="one in desc" :key="one.key">
                    <span class="color-desc">{{ one.title }}：</span>{{ data[one.key] }}
                  </li>
                </ul>
              </div>
            </div>
            <i class="i-ph-note-pencil ml-2 cursor-pointer color-desc" @click="handleEdit" />
          </div>
        </div>
        <div flex flex-1 flex-col overflow-hidden>
          <div relative h-full w-full>
            <el-tabs v-model="activeName" class="custom-tabs h-full w-full">
              <el-tab-pane label="规则信息" name="1">
                <RuleList ref="ruleListRef" />
              </el-tab-pane>
              <el-tab-pane label="调度配置" name="2">
                <ScheduleList ref="scheduleListRef" />
              </el-tab-pane>
              <el-tab-pane label="告警配置" name="3">
                <WarningList ref="warningListRef" />
              </el-tab-pane>
            </el-tabs>
            <div absolute right-0 top-3 flex items-center gap-2.5>
              <template v-if="activeName === '1'">
                <el-input v-model="formStateOfRule.ruleName" placeholder="请输入搜索关键字" class="w-62!" clearable @clear="handleRuleQuery" @keyup.enter="handleRuleQuery">
                  <template #suffix>
                    <i class="i-ph-magnifying-glass cursor-pointer" @click="handleRuleQuery" />
                  </template>
                </el-input>
                <el-button type="primary" @click="handleCreateRule">
                  新建规则
                </el-button>
              </template>
              <template v-else-if="activeName === '2'">
                <el-button type="primary" @click="handleCreateSchedule">
                  新增调度
                </el-button>
              </template>
              <template v-else-if="activeName === '3'">
                <el-button type="primary" @click="handleCreateWarning">
                  新建告警配置
                </el-button>
              </template>
            </div>
          </div>
          <!-- <div class="flex-1 overflow-hidden bg-white">
            <RuleList />
          </div> -->
        </div>
      </div>
    </BaseContent>
    <RuleSetEdit ref="detailRef" @success="query" />
  </div>
</template>

<style lang="scss" scoped>
.custom-tabs.el-tabs {
  :deep(.el-tabs__nav-wrap::after) {
    background-color: unset !important;
  }
  :deep(.el-tab-pane) {
    @apply h-full;
  }
}
</style>
