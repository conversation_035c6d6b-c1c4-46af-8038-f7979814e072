<script lang="ts" setup>
import type { RuleScheduleCreateParams, RuleScheduleUpdateParams } from '~/services/rule'
import cloneDeep from 'lodash.clonedeep'
import { ruleScheduleServices } from '~/services/rule'

const emit = defineEmits(['success'])

const dialogRef = ref()
const formRef = ref()

const model = ref<RuleScheduleCreateParams | RuleScheduleUpdateParams>({
  rulesetId: -1,
  scheduleName: '',
  scheduleType: '',
  cronExpression: '',
  id: -1,
})
const rules = ref({
  scheduleName: [{ required: true, message: '请输入调度名称', trigger: 'blur' }],
  scheduleType: [{ required: true, message: '请选择调度类型', trigger: 'blur' }],
  cronExpression: [{ required: true, message: '请输入cron表达式', trigger: 'blur' }],
})
const submitLoading = ref(false)
const isEdit = ref(false)

async function open(title: string, params = {}) {
  isEdit.value = !!params.id
  dialogRef.value.open({ title })
  if (!isEdit.value) {
    model.value = { ...cloneDeep(params), rulesetId: useRulesStore().currentRuleSetId as number } as RuleScheduleCreateParams
  }
  else {
    const result = await ruleScheduleServices.detail(params.id!)
    const { createAt, createBy, createByName, updateAt, updateBy, updateByName, ...rest } = result
    model.value = { ...rest }
  }
  if (!model.value.scheduleType)
    model.value.scheduleType = 'PERIOD_SCHEDULE'
}

function close() {
  dialogRef.value.close()
}

function handleSubmit() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        submitLoading.value = true
        if (!isEdit.value) {
          await ruleScheduleServices.create(model.value)
          ElMessage.success('创建成功')
        }
        else {
          await ruleScheduleServices.update(model.value as RuleScheduleUpdateParams)
          ElMessage.success('保存成功')
        }
        close()
        emit('success', model.value as RuleScheduleCreateParams | RuleScheduleUpdateParams)
      }
      catch (error) {
        console.error(error)
      }
      finally {
        submitLoading.value = false
      }
    }
  })
}

defineExpose({
  open,
})
</script>

<template>
  <BaseDialog ref="dialogRef" :confirm-loading="submitLoading" width="560px" @confirm="handleSubmit">
    <el-form ref="formRef" :model="model" :rules="rules" label-width="100px" label-position="right" class="pr-4">
      <el-form-item label="调度名称" prop="scheduleName">
        <el-input v-model="model.scheduleName" />
      </el-form-item>
      <el-form-item label="调度类型" prop="scheduleType">
        <CustomRadio v-model="model.scheduleType" option-key="scheduleType" />
      </el-form-item>
      <el-form-item label="cron表达式" prop="cronExpression">
        <el-input v-model="model.cronExpression" />
      </el-form-item>
    </el-form>
  </BaseDialog>
</template>
