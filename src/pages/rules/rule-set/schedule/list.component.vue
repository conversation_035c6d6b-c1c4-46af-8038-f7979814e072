<script lang="ts" setup>
import { ruleScheduleServices } from '~/services/rule'
import ScheduleEdit from '../schedule/detail.component.vue'

const id = +useRoute().params.id as number
const tableRef = ref()
const otherParams = ref({})

const scheduleEditRef = ref()
const columns = ref([
  { label: '调度名称', prop: 'scheduleName' },
  { label: '调度类型', prop: 'scheduleType', optionKey: 'scheduleType' },
  { label: '最后更新人', prop: 'updateByName' },
  { label: '最后更新时间', prop: 'updateAt' },
])

const actions = ref([
  {
    name: '编辑',
    handler: ({ row }: { row: any }) => {
      scheduleEditRef.value.open('编辑调度', row)
    },
  },
  {
    name: '删除',
    handler: async ({ row }: { row: any }) => {
      try {
        await ruleScheduleServices.delete(row.id)
        ElMessage.success('删除成功')
        query()
      }
      catch (e) {
        console.error(e)
      }
    },
  },
])

const api = reactive({
  fn: ruleScheduleServices.list,
  getParams: () => ({
    rulesetId: id,
    ...otherParams.value,
  }),
  resultFilter: (res: any) => res,
})

function create() {
  scheduleEditRef.value.open('新增调度')
}

function query(params?: Record<string, any>) {
  otherParams.value = params || otherParams.value
  tableRef.value.query({ initPage: true })
}

defineExpose({
  create,
  query,
})
</script>

<template>
  <SearchTable ref="tableRef" :columns="columns" :api="api" :actions="actions" :show-page="false" />
  <ScheduleEdit ref="scheduleEditRef" @success="query" />
</template>
