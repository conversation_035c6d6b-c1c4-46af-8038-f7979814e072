<script lang="ts" setup>
import type { CascaderProps } from 'element-plus'
import { DICTS } from '~/enums/dict'
import { ruleTemplateServices } from '~/services/rule'

const props: CascaderProps = {
  lazy: true,
  async lazyLoad(node, resolve) {
    const { level } = node
    if (level === 0)
      return resolve(DICTS.ruleType)
    return resolve((await ruleTemplateServices.list(node.data!.value as string) || []).map(item => ({
      ...item,
      label: item.templateName,
      value: item.id,
      leaf: true,
    })))
  },
}
</script>

<template>
  <el-cascader :props="props" v-bind="$attrs" />
</template>
