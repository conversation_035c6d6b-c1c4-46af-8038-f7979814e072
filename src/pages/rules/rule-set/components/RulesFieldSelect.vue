<script lang="ts" setup>
import { ruleStateKey } from '~/enums/provide'
import { dataSetService } from '~/services/data-set'

const props = defineProps<{
  dataSetId?: number
  useProp?: boolean // 是否使用父组件传递的dataSetId属性
}>()

const value = ref()
const options = ref([])

const { getParentRef } = inject(ruleStateKey)!

// 默认使用监控对象模块-对象名称
const dataSetId = computed(() => {
  return props.useProp ? props.dataSetId : getParentRef('monitor')?.getData().watchId
})

watch(() => dataSetId.value, () => {
  query(dataSetId.value)
})

onMounted(() => {
  query(dataSetId.value)
})

async function query(id: string) {
  await nextTick()
  try {
    if (!id)
      return (options.value = [])
    const result = await dataSetService.meta({ dataSetId: id })
    options.value = result.map(item => ({
      ...item,
      label: `${item.columnName} (${item.columnAlias})`,
    }))
  }
  catch (error) {
    console.error(error)
  }
}

defineExpose({
  query,
})
</script>

<template>
  <el-select-v2 v-model="value" :options="options" filterable :props="{ value: 'id', label: 'label', options: 'options' }" v-bind="$attrs" />
</template>
