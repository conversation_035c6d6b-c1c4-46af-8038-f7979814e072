<script lang="ts" setup>
import { getStateFromFields } from '~/components/DynamicForm'
import DynamicForm from '~/components/DynamicForm/DynamicForm.vue'
import { useSchedule } from '~/composables/useSchedule'
import { ruleSetServices } from '~/services/rule-set'

const emit = defineEmits(['success'])
const { queryScheduleList } = useSchedule()

const baseDialogRef = ref()
const formRef = ref()
const scheduleList = ref([])

const fields = ref({
  scheduleId: {
    required: true,
    type: 'select2',
    label: '调度',
    bindings: {
      options: computed(() => scheduleList.value),
      props: {
        label: 'scheduleName',
        value: 'id',
        desc: 'scheduleTypeName',
      },

    },
  },
  bizDate: {
    type: 'date',
    label: '业务日期',
    required: true,
  },
})
const model = ref<{ bizDate: string, scheduleId: number }>(getStateFromFields(fields.value) as { bizDate: string, scheduleId: number })
const rulesetId = ref<number | null>(null)
const submitLoading = ref(false)

async function open(params: { id: number }) {
  rulesetId.value = params.id
  baseDialogRef.value.open()

  await nextTick()
  await setTimeout(() => {}, 0)
  formRef.value?.clearValidate()

  scheduleList.value = await queryScheduleList(params.id)
}

function close() {
  baseDialogRef.value.close()
}

async function handleConfirm() {
  const valid = await formRef.value?.validate()
  if (!valid)
    return
  submitLoading.value = true
  try {
    await ruleSetServices.run({ ...model.value, rulesetId: rulesetId.value as number })
    ElMessage.success('任务开始执行')
    baseDialogRef.value.close()
    emit('success')
  }
  catch (e) {
    console.error(e)
  }
  finally {
    submitLoading.value = false
  }
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <BaseDialog ref="baseDialogRef" confirm-text="运行" width="560px" :confirm-loading="submitLoading" @confirm="handleConfirm">
    <DynamicForm ref="formRef" v-model="model" :fields="fields" :use-grid="false" label-width="80px" />
  </BaseDialog>
</template>
