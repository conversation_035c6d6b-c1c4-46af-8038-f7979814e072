<script lang="ts" setup>
import type { CheckboxValueType } from 'element-plus'
import { FETCH_DICT } from '~/enums/dict'
import { ruleServices, ruleWarningServices } from '~/services/rule'
import { CoverageTypeEnum } from '~/types/enum'
import { stringify } from '~/utils/type'

const props = defineProps<{
  rulesetId: number
}>()

const emit = defineEmits<{
  (e: 'success'): void
}>()

const baseDialogRef = ref()
const formRef = ref()
const state = ref()
const confirmLoading = ref(false)
const model = ref<{
  coverageType: CoverageTypeEnum
  ruleCollection: any[]
  alertConfigName: string
  alertReceivers: any[]
  alertMethods: any[]
  id?: number
}>({
  coverageType: CoverageTypeEnum.ALL, // 覆盖范围
  ruleCollection: [], // 规则集合
  alertConfigName: '', // 告警配置名称
  alertReceivers: [], // 告警接收人
  alertMethods: [], // 告警方式
})
const ruleList = ref([])
const checkAll = ref(false)
const indeterminate = ref(false)
const isEdit = computed(() => state.value?.id)
const title = computed(() => isEdit.value ? '编辑规则告警' : '新增规则告警')
const rules = ref({
  coverageType: [{ required: true, validator: coverageTypeValidator, trigger: 'change' }],
  alertConfigName: [{ required: true, message: '请输入告警配置名称', trigger: 'blur' }],
  alertReceivers: [{ required: true, message: '请选择告警接收人', trigger: 'change' }],
  alertMethods: [{ required: true, message: '请选择告警方式', trigger: 'change' }],
})

function coverageTypeValidator(rule: any, value: CheckboxValueType, callback: (params?: any) => void) {
  if (model.value.coverageType === CoverageTypeEnum.CUSTOM && model.value.ruleCollection.length === 0) {
    return callback(new Error('请选择规则'))
  }
  return callback()
}

function open(params) {
  state.value = params
  baseDialogRef.value?.open()
  if (params?.id)
    init(params)
}

function handleCheckAll(val: CheckboxValueType) {
  indeterminate.value = false
  if (val) {
    model.value.ruleCollection = ruleList.value.map(_ => _.id)
  }
  else {
    model.value.ruleCollection = []
  }
}

async function queryRuleList() {
  try {
    const res = await ruleServices.allList({ rulesetId: props.rulesetId })
    ruleList.value = res
  }
  catch (e) {
    console.error(e)
  }
}

function init(params) {
  const { coverageType, ruleCollection, alertConfigName, alertReceivers, alertMethods } = params

  model.value = {
    coverageType: coverageType || CoverageTypeEnum.ALL,
    ruleCollection: ruleCollection ? JSON.parse(ruleCollection) : [],
    alertConfigName,
    alertReceivers: alertReceivers ? JSON.parse(alertReceivers) : [],
    alertMethods: (alertMethods || []).split(','),
  }
}

function handleSubmit() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const { ruleCollection, alertReceivers } = model.value
      const _alertReceivers = alertReceivers?.map(_ => ({ userId: _.userId, userName: _.userName, mobile: _.mobile, email: _.email }))
      const _ruleCollection = ruleCollection?.map(_ => ({ id: _.id, ruleName: _.ruleName }))

      const hasNoContactOne = _alertReceivers.some(_ => !_.mobile || !_.email)
      if (hasNoContactOne) {
        ElMessage.error('所有接收人的电话和邮箱不可以为空，请登录天权系统进行维护')
        return
      }

      try {
        confirmLoading.value = true
        const api = isEdit.value ? ruleWarningServices.update : ruleWarningServices.save

        const params = {
          ...model.value,
          rulesetId: props.rulesetId,
          id: isEdit.value ? state.value?.id : undefined,
          alertReceivers: _alertReceivers?.length ? stringify(_alertReceivers) : null,
          ruleCollection: _ruleCollection?.length ? stringify(_ruleCollection) : null,
          alertMethods: (model.value.alertMethods || []).join(','),
        }
        await api(params)
        ElMessage.success(`${isEdit.value ? '修改告警配置' : '新建告警配置'} 成功`)
        emit('success')
        baseDialogRef.value.close()
      }
      catch (e) {
        console.error(e)
      }
      finally {
        confirmLoading.value = false
      }
    }
  })
}

onMounted(() => {
  queryRuleList()
})

defineExpose({
  open,
})
</script>

<template>
  <BaseDialog ref="baseDialogRef" :title="title" width="650" :confirm-loading="confirmLoading" @confirm="handleSubmit">
    <el-form ref="formRef" :model="model" label-width="110px" :rules="rules">
      <el-form-item label="覆盖范围" prop="coverageType">
        <div class="w-full">
          <div class="mb-2 flex items-center">
            <el-radio v-model="model.coverageType" class="flex-[0_0_80px]" label="所有规则" :value="CoverageTypeEnum.ALL" />
            <el-radio v-model="model.coverageType" label="所有强规则" :value="CoverageTypeEnum.STRONG" />
            <el-radio v-model="model.coverageType" label="所有弱规则" :value="CoverageTypeEnum.WEAK" />
          </div>
          <div class="flex items-center">
            <el-radio v-model="model.coverageType" class="flex-[0_0_80px]" label="自定义" :value="CoverageTypeEnum.CUSTOM" />
            <el-select v-model="model.ruleCollection" collapse-tags collapse-tags-tooltip multiple value-key="id" :max-collapse-tags="3">
              <template v-if="ruleList.length > 0" #header>
                <el-checkbox
                  v-model="checkAll"
                  :indeterminate="indeterminate"
                  @change="handleCheckAll"
                >
                  全选
                </el-checkbox>
              </template>
              <el-option
                v-for="item in ruleList"
                :key="item.id"
                :label="item.ruleName"
                :value="item"
              />
            </el-select>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="告警配置名称" prop="alertConfigName">
        <el-input v-model="model.alertConfigName" placeholder="请输入告警配置名称" />
      </el-form-item>
      <el-form-item label="告警接收人" prop="alertReceivers">
        <div class="w-full flex items-center gap-2">
          <CustomSelectV2 v-model="model.alertReceivers" option-key="userList" value-key="userId" :props="{ ...FETCH_DICT.get('userList')?.props, value: 'options' }" multiple>
            <template #default="{ item }">
              <div class="flex items-center justify-between">
                <div>{{ item.userName }}({{ item.userId }})</div>
                <div class="flex items-center text-desc fs-12">
                  <span v-if="!item.email">未配置邮箱</span>
                  <template v-if="!item.email && !item.mobile">
                    ,
                  </template>
                  <span v-if="!item.mobile">未配置电话</span>
                </div>
              </div>
            </template>
          </CustomSelectV2>
        </div>
      </el-form-item>
      <el-form-item label="告警方式" prop="alertMethods">
        <CustomSelect v-model="model.alertMethods" option-key="alertType" multiple />
      </el-form-item>
    </el-form>
  </BaseDialog>
</template>
