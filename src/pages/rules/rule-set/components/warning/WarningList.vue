<script lang="ts" setup>
import { DICTS } from '~/enums/dict'
import { ruleWarningServices } from '~/services/rule'
import { CoverageTypeEnum } from '~/types/enum'
// import ScheduleEdit from '../schedule/detail.component.vue'
import { parse } from '~/utils/type'

const id = +useRoute().params.id as number
const tableRef = ref()
const otherParams = ref({})

const warningEditRef = ref()
const columns = ref([
  { label: '告警配置名称', prop: 'alertConfigName' },
  { label: '生效范围', prop: 'scope', showOverflowTooltip: true },
  { label: '告警接收人-自定义', prop: '_alertReceivers' },
  { label: '告警方式', prop: '_alertMethods' },
])

const actions = ref([
  {
    name: '编辑',
    handler: ({ row }: { row: any }) => {
      warningEditRef.value.open(row)
    },
  },
  {
    name: '删除',
    handler: async ({ row }: { row: any }) => {
      try {
        await ruleWarningServices.delete(row.id)
        ElMessage.success('删除成功')
        query()
      }
      catch (e) {
        console.error(e)
      }
    },
  },
])

const api = reactive({
  fn: ruleWarningServices.list,
  getParams: () => ({
    rulesetId: id,
    ...otherParams.value,
  }),
  resultFilter: (res: any) => {
    const result = res.map((item) => {
      const _ruleCollection = item.ruleCollection ? parse(item.ruleCollection) : []
      return {
        ...item,
        _alertReceivers: item.alertReceivers ? parse(item.alertReceivers).map(_ => _.userName).join(',') : '',
        _alertMethods: item.alertMethods ? item.alertMethods.split(',').map(_ => DICTS.alertType.find(i => i.value === _)?.label).join(',') : '',
        scope: item.coverageType !== CoverageTypeEnum.CUSTOM ? DICTS.coverageType.find(_ => _.value === item.coverageType)?.label : `${_ruleCollection.length}条规则：${_ruleCollection.map(_ => _.ruleName).join(',')}`,
      }
    })
    return result
  },
})

function create() {
  warningEditRef.value.open()
}

function query(params?: Record<string, any>) {
  otherParams.value = params || otherParams.value
  tableRef.value.query({ initPage: true })
}

defineExpose({
  create,
  query,
})
</script>

<template>
  <SearchTable ref="tableRef" :columns="columns" :api="api" :actions="actions" :show-page="false" />
  <WarningDetail ref="warningEditRef" :ruleset-id="id" @success="query" />
</template>
