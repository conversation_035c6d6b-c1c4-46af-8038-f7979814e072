<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import BaseDialog from '~/components/BaseDialog.vue'
import { getStateFromFields } from '~/components/DynamicForm'
import { FETCH_DICT } from '~/enums/dict'
import { ruleSetServices } from '~/services/rule-set'

const emit = defineEmits<{
  (event: 'success', type: 'edit' | 'create', params: any): void
}>()
const visible = ref(false)
const submitLoading = ref(false)
const isEdit = ref(false)
const dialogTitle = ref('')
const formRef = ref()

const fields = ref({
  name: {
    label: '规则集名称',
    type: 'input',
    required: true,
  },
  qualityPersons: {
    label: '质量负责人',
    required: true,
    type: 'select2',
    bindings: {
      optionKey: 'userList',
      valueKey: 'userId',
      props: { ...FETCH_DICT.get('userList')?.props, value: 'options' },
      multiple: true,
    },
  },
  projectId: {
    label: '项目',
    required: true,
    type: 'select2',
    bindings: {
      optionKey: 'projectList',
    },
  },
  qualityScore: {
    label: '质量分权重',
    required: true,
    type: 'inputNumber',
    bindings: {
      optionKey: 'dataSourceList',
      controls: true,
      min: 0,
      max: 10,
      style: {
        width: '200px',
      },
    },
  },
  remark: {
    label: '描述',
    type: 'input',
    bindings: {
      type: 'textarea',
    },
  },
})
const model = ref<any>(getStateFromFields(fields.value))

async function open(title: string, params: any) {
  dialogTitle.value = title
  isEdit.value = !!params?.id
  if (isEdit.value)
    model.value = await query(params.id)
  else
    model.value = { ...getStateFromFields(fields.value), ...(params || {}) }
  visible.value = true

  await nextTick()
  await setTimeout(() => {}, 0)
  formRef.value.clearValidate()
}
function close() {
  visible.value = false
}

async function query(id: number) {
  try {
    const result = await ruleSetServices.detail(id)
    return result
  }
  catch (e) {
    console.error(e)
    return null
  }
}

async function handleSubmit() {
  const result = await formRef.value.validate()
  if (!result)
    return
  submitLoading.value = true
  try {
    const { name, projectId, remark, qualityPersons, id, qualityScore } = model.value
    const _qualityPersons = qualityPersons.map(item => ({
      id: item.id,
      rulesetId: item.rulesetId || useRulesStore().currentRuleSetId,
      userId: item.userId,
      userName: item.userName,
    }))
    let result
    if (isEdit.value) {
      const params = { name, projectId, remark, qualityPersons: _qualityPersons, id, qualityScore }
      result = await ruleSetServices.update(params)
      ElMessage.success('编辑成功')
    }
    else {
      result = await ruleSetServices.create({ name, projectId, remark, qualityPersons: _qualityPersons, qualityScore })
      ElMessage.success('新增成功')
    }
    emit('success', isEdit.value ? 'edit' : 'create', { ...model.value, id: result })
    close()
  }
  catch (e) {
    console.error(e)
  }
  finally {
    submitLoading.value = false
  }
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <BaseDialog
    v-model="visible" :title="dialogTitle"
    :confirm-loading="submitLoading"
    :confirm-text="isEdit ? '保存' : '下一步'"
    @cancel="close"
    @confirm="handleSubmit"
  >
    <DynamicForm ref="formRef" v-model="model" :fields="fields" :use-grid="false" label-width="100px" />
  </BaseDialog>
</template>
