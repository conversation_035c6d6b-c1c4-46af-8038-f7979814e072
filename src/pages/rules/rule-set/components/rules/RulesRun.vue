<script lang="ts" setup>
import { useSchedule } from '~/composables/useSchedule'
import { DICTS } from '~/enums/dict'
import { ruleServices } from '~/services/rule'
import { PartitionExpressionFromEnum } from '~/types/enum'

const emit = defineEmits(['success'])
const { queryScheduleList } = useSchedule()

const baseDialogRef = ref()
const formRef = ref()
const scheduleList = ref([])

const model = ref<{ partitionExpressionFrom: PartitionExpressionFromEnum, partitionExpression: string | null, bizDate: string, scheduleId: number | null }>({
  partitionExpressionFrom: PartitionExpressionFromEnum.SCHEDULE,
  scheduleId: null,
  bizDate: '',
  partitionExpression: null,
})
const rulesetId = ref<number | null>(null)
const state = ref()
const submitLoading = ref(false)

const rules = ref({
  scheduleId: [
    { required: true, message: '请选择调度', trigger: 'change' },
  ],
  partitionExpression: [
    { required: true, message: '请选择自定义校验范围', trigger: 'change' },
  ],
  bizDate: [
    { required: true, message: '请选择业务日期', trigger: 'change' },
  ],
})

async function open(params: { id: number, rulesetId: number }) {
  state.value = params
  rulesetId.value = params.rulesetId
  baseDialogRef.value.open()

  await nextTick()
  await setTimeout(() => {}, 0)
  formRef.value?.clearValidate()

  scheduleList.value = await queryScheduleList(params.rulesetId)
}

function close() {
  baseDialogRef.value.close()
}

async function handleConfirm() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitLoading.value = true
      try {
        const params = {
          ...model.value,
          rulesetId: rulesetId.value as number,
          ruleIds: [state.value.id],
        }
        const deleteKey = model.value.partitionExpressionFrom === PartitionExpressionFromEnum.SCHEDULE ? 'partitionExpression' : 'scheduleId'
        delete params[deleteKey]
        await ruleServices.run(params as any)
        ElMessage.success('任务开始执行')
        emit('success')
        baseDialogRef.value.close()
      }
      catch (e) {
        console.error(e)
      }
      finally {
        submitLoading.value = false
      }
    }
  })
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <BaseDialog ref="baseDialogRef" confirm-text="运行" width="560px" :confirm-loading="submitLoading" @confirm="handleConfirm">
    <el-form ref="formRef" label-width="100px" :model="model" :rules="rules">
      <el-form-item label="试跑范围" :prop="model.partitionExpressionFrom === PartitionExpressionFromEnum.CUSTOM ? 'partitionExpression' : 'scheduleId'">
        <div class="mb-1 flex items-center">
          <el-radio v-model="model.partitionExpressionFrom" style="width: 190px" :label="DICTS.partitionExpressionFromList[0]?.label" :value="DICTS.partitionExpressionFromList[0]?.value" />
          <CustomSelectV2
            v-model="model.scheduleId"
            :options="scheduleList" :props=" {
              label: 'scheduleName',
              value: 'id',
              desc: 'scheduleTypeName',
            }"
          />
        </div>
        <div class="flex items-center">
          <el-radio v-model="model.partitionExpressionFrom" style="width: 190px" :label="DICTS.partitionExpressionFromList[1]?.label" :value="DICTS.partitionExpressionFromList[1]?.value" />
          <CustomSelectV2 v-model="model.partitionExpression" option-key="customPartitionExpression" />
        </div>
      </el-form-item>
      <el-form-item label="业务日期" prop="bizDate">
        <CustomDate v-model="model.bizDate" />
      </el-form-item>
      <el-form-item v-if="model.partitionExpressionFrom === PartitionExpressionFromEnum.CUSTOM" label="校验范围预算">
        {{ model.partitionExpression }}
      </el-form-item>
    </el-form>
  </BaseDialog>
</template>
