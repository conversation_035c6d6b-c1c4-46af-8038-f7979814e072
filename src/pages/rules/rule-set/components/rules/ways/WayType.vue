<script lang="ts" setup>
import { ruleFormConfigKey, ruleStateKey } from '~/enums/provide'
import { isValidNumber, parse, stringify } from '~/utils/type'

const props = defineProps<{
  templateId: number
}>()

const formCommonConfig = inject(ruleFormConfigKey)!
const { data, isEdit } = inject(ruleStateKey)!

const formRef = ref()
const model = ref({
  field: null,
  valueRangeType: 'number', // 值域类型
  enumType: 'in',
  enumValue: [],
  intervalLeftType: '>',
  intervalRightType: '<',
  intervalLeftValue: undefined,
  intervalRightValue: undefined,
})
// 基本表单校验规则
const basicFormRules = ref({
  field: [{ required: true, message: '请选择字段' }],
  valueRangeType: [{ required: true, message: '请选择值域类型' }],
})
// 枚举校验规则
const enumRules = ref({
  enumValue: [{ required: true, message: '请输入枚举值' }],
})
// 区间校验规则
const rangeRules = ref({
  intervalLeftValue: [{ required: true, validator: intervalValueValidator }],
})

// 值域范围 枚举 | 区间
const rangeType = computed(() => [400].includes(props.templateId) ? 'enum' : [500].includes(props.templateId) ? 'range' : undefined)
const isEnum = computed(() => rangeType.value === 'enum')
const isRange = computed(() => rangeType.value === 'range')

const isTextType = computed(() => model.value.valueRangeType === 'text')
const isNumberType = computed(() => model.value.valueRangeType === 'number')
// const isDateType = computed(() => model.value.valueRangeType === 'date')

const rules = computed(() => {
  return { ...basicFormRules.value, ...(isEnum.value ? enumRules.value : isRange.value ? rangeRules.value : {}) }
})

onMounted(() => {
  if (isEdit.value) {
    init()
  }
})

function intervalValueValidator(rule, value, callback) {
  if (!isValidNumber(model.value.intervalLeftValue) || !isValidNumber(model.value.intervalRightValue)) {
    return callback(new Error('请输入区间值'))
  }
  callback()
}

function init() {
  const validateObj = data.value.validateObjects ? parse(data.value.validateObjects) : []
  const target = validateObj[0] || {}
  const { valueRangeType, enumType, enumValue, intervalLeftType, intervalRightType, intervalLeftValue, intervalRightValue } = target.otherConfig || {}
  model.value = {
    field: target.colMetaDataId,
    valueRangeType: valueRangeType || 'number',
    enumType: enumType || 'in',
    enumValue: enumValue ? enumValue.split(',').map((v: string) => v.replace(/'/g, '')) : [],
    intervalLeftType: intervalLeftType || '>',
    intervalRightType: intervalRightType || '<',
    intervalLeftValue,
    intervalRightValue,
  }
}

defineExpose({
  getData: () => {
    const { field, valueRangeType, enumType, enumValue, intervalLeftType, intervalLeftValue, intervalRightType, intervalRightValue } = model.value

    const otherConfig = isEnum.value
      ? {
          valueRangeType,
          enumType,
          enumValue: enumValue.map(v => `'${v}'`).join(','),
        }
      : {
          valueRangeType,
          intervalLeftType,
          intervalLeftValue,
          intervalRightType,
          intervalRightValue,
        }

    return {
      validateObjects: stringify([{
        colMetaDataId: field,
        otherConfig,
      }]),
    }
  },
  validate: () => {
    return new Promise((resolve) => {
      formRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
  },
})
</script>

<template>
  <el-form ref="formRef" v-bind="formCommonConfig" :model="model" :rules="rules">
    <el-row>
      <el-col :span="12">
        <el-form-item label="校验字段" prop="field">
          <RulesFieldSelect v-model="model.field" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="值域类型" prop="valueRangeType">
          <CustomSelectV2 v-model="model.valueRangeType" option-key="dataValueType" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item v-if="isEnum" label="值域范围" prop="enumValue">
          <div class="w-full flex gap-1">
            <el-radio v-model="rangeType" label="枚举" value="enum" />
            <CustomSelectV2 v-model="model.enumType" class="flex-[0_0_80px]" option-key="enumType" />
            <el-input-tag v-model="model.enumValue" class="flex-1 max-w-unset!" />
          </div>
        </el-form-item>
        <el-form-item v-if="!isTextType && isRange" label="值域类型" prop="intervalLeftValue">
          <div class="w-full flex gap-1">
            <el-radio v-model="rangeType" label="区间" value="range" />
            <CustomSelectV2 v-model="model.intervalLeftType" disabled class="flex-[0_0_80px]" option-key="calcSymbol" />
            <CustomInputNumber v-if="isNumberType" v-model="model.intervalLeftValue" class="flex-1 max-w-unset!" />
            <CustomDate v-else />
            <CustomSelectV2 v-model="model.intervalRightType" disabled class="flex-[0_0_80px]" option-key="calcSymbol" />
            <CustomInputNumber v-if="isNumberType" v-model="model.intervalRightValue" class="flex-1 max-w-unset!" />
            <CustomDate v-else />
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
