<script lang="ts" setup>
import { ruleConfigStateKey, ruleFormConfig<PERSON>ey, ruleStateKey } from '~/enums/provide'
import { parse, stringify } from '~/utils/type'

const formCommonConfig = inject(ruleFormConfigKey)!
const { data, isEdit } = inject(ruleStateKey)!
const { formState } = inject(ruleConfigStateKey)!

const formRef = ref()
const model = ref<{
  fields: number[] | number | null
}>({
  fields: [],
})
const rules = ref({
  fields: [{ required: true, message: '请选择校验字段', trigger: 'change' }],
})

// 唯一性校验字段是多选
const isMultiple = computed(() => formState.templateId === 300)

watch(() => formState.templateId, () => {
  if (formState.templateId === 300) {
    model.value = { fields: [] }
  }
  else {
    model.value = { fields: null }
  }
})

onMounted(() => {
  if (isEdit.value) {
    init()
  }
})

function init() {
  const validObj = data.value.validateObjects ? parse(data.value.validateObjects) : []
  model.value = {
    fields: isMultiple.value ? validObj.map((item: any) => item.colMetaDataId) : validObj[0].colMetaDataId,
  }
}

defineExpose({
  init,
  getData: () => {
    const { fields } = model.value
    return {
      validateObjects: stringify(isMultiple.value ? (fields as number[]).map(field => ({ colMetaDataId: field })) : [{ colMetaDataId: fields as number }]), // 校验字段
    }
  },
  validate: () => {
    return new Promise((resolve) => {
      formRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
  },
})
</script>

<template>
  <el-form ref="formRef" :model="model" :rules="rules" v-bind="formCommonConfig">
    <el-form-item label="校验字段" prop="fields">
      <RulesFieldSelect v-model="model.fields" :multiple="formState.templateId === 300" />
    </el-form-item>
  </el-form>
</template>
