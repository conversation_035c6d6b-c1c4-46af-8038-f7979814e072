<script lang="ts" setup>
import { ruleFormConfigKey, ruleStateKey } from '~/enums/provide'
import { parse, stringify } from '~/utils/type'

// 定义类型
interface ValidationRule {
  required?: boolean
  message?: string
  trigger?: string
  validator?: (rule: any, value: any, callback: any) => void
}

interface FormModel {
  field: string
  compareDataSetId: number | undefined
  compareField: number | undefined
  joinMethod: string
  doubleTableJoinCondition: string
  joinConditionMappings: any[]
  // 新增精度阀值字段
  thresholdOperator: string
  thresholdValue: number | undefined
}

const formCommonConfig = inject(ruleFormConfigKey)!
const { data, isEdit } = inject(ruleStateKey)!
const { getParentRef } = inject(ruleStateKey)!

const configRef = ref() // 配置关联表达式弹窗
const formRef = ref()
const model = ref<FormModel>({
  field: '',
  compareDataSetId: undefined,
  compareField: undefined,
  joinMethod: 'left join',
  doubleTableJoinCondition: '',
  joinConditionMappings: [],
  // 新增精度阀值字段
  thresholdOperator: '>',
  thresholdValue: undefined,
})

const rules = ref<Record<string, ValidationRule[]>>({
  field: [{ required: true, message: '请选择校验字段', trigger: 'change' }],
  compareDataSetId: [{ required: true, validator: compareValidator, trigger: 'change' }],
  compareField: [{ required: true, validator: compareValidator, trigger: 'change' }],
  joinMethod: [{ required: true, message: '请选择关联方式', trigger: 'change' }],
  doubleTableJoinCondition: [{ required: true, message: '请配置关联表达式', trigger: 'blur' }],
  // 新增精度阀值验证规则
  thresholdOperator: [{ required: true, message: '请选择操作符', trigger: 'change' }],
  thresholdValue: [{ required: true, message: '请输入阀值', trigger: 'blur' }],
})

// 精度阀值操作符选项
const thresholdOperatorOptions = [
  { label: '>', value: '>' },
  { label: '>=', value: '>=' },
  { label: '<', value: '<' },
  { label: '<=', value: '<=' },
  { label: '!=', value: '!=' },
]

// 表达式配置字段只读
const isExpressionDisabled = computed(() => {
  return !getParentRef('monitor')?.getData().watchId || !model.value.compareDataSetId || !model.value.compareField
})

function handleChangeCompareDataSetId() {
  model.value.compareField = undefined
  resetExpression()
}

// 清空表达式相关内容
function resetExpression() {
  model.value.doubleTableJoinCondition = ''
  model.value.joinConditionMappings = []
}

function compareValidator(rule: any, value: any, callback: any) {
  if (!model.value.compareDataSetId) {
    return callback(new Error('请选择比较表'))
  }
  if (!model.value.compareField) {
    return callback(new Error('请选择比较字段'))
  }
  callback()
}

onMounted(() => {
  if (isEdit.value) {
    init()
  }
})

function init() {
  const validObj = data.value.validateObjects ? parse(data.value.validateObjects) : []
  const compareObj = data.value.compareObject ? parse(data.value.compareObject) : {}

  model.value = {
    field: validObj[0]?.colMetaDataId,
    compareDataSetId: compareObj.otherConfig?.dataSetId,
    compareField: compareObj.colMetaDataId,
    joinMethod: compareObj.otherConfig?.joinMethod || 'left join',
    doubleTableJoinCondition: compareObj.otherConfig?.doubleTableJoinCondition || '',
    joinConditionMappings: compareObj.otherConfig?.joinConditionMappings || [],
    // 新增精度阀值字段的初始化 - 从顶级获取
    thresholdOperator: data.value.thresholdOperator || '>',
    thresholdValue: data.value.thresholdValue,
  }
}

// 配置关联表达式
function handleConfig() {
  const params = {
    leftTableId: getParentRef('monitor')?.getData().watchId,
    rightTableId: model.value.compareDataSetId,
  }
  configRef.value.open(params, model.value.joinConditionMappings)
}

function handleConfigSuccess(result: any, resultStr: string) {
  model.value.doubleTableJoinCondition = resultStr
  model.value.joinConditionMappings = result
}

defineExpose({
  getData: () => {
    const { field, compareDataSetId, compareField, joinMethod, doubleTableJoinCondition, joinConditionMappings, thresholdOperator, thresholdValue } = model.value
    return {
      validateObjects: stringify([{ colMetaDataId: field }]),
      compareObject: stringify({
        colMetaDataId: compareField,
        otherConfig: {
          dataSetId: compareDataSetId,
          joinMethod,
          doubleTableJoinCondition,
          joinConditionMappings,
        },
      }),
      compareDataSetId,
      // 精度阀值字段保存到顶级
      thresholdOperator,
      thresholdValue,
    }
  },
  validate: () => {
    return new Promise((resolve) => {
      formRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
  },
  resetExpression,
  onWatchIdChange: () => {
    model.value.field = ''
    resetExpression()
  },
})
</script>

<template>
  <el-form ref="formRef" :model="model" :rules="rules" v-bind="formCommonConfig">
    <el-form-item label="校验字段" prop="field">
      <RulesFieldSelect v-model="model.field" />
    </el-form-item>
    <el-form-item label="选择比较表" prop="compareDataSetId">
      <div class="w-full flex gap-2.5">
        <CustomAsyncSelect v-model="model.compareDataSetId" option-key="dataSetSelectList" :immediate="true" @change="handleChangeCompareDataSetId" />
        <RulesFieldSelect v-model="model.compareField" use-prop :data-set-id="model.compareDataSetId" />
      </div>
    </el-form-item>
    <el-form-item label="关联方式" prop="joinMethod">
      <CustomSelectV2 v-model="model.joinMethod" option-key="joinType" />
    </el-form-item>
    <el-form-item label="关联表达式" prop="doubleTableJoinCondition">
      <el-input v-model="model.doubleTableJoinCondition" readonly :disabled="isExpressionDisabled">
        <template #append>
          <div class="cursor-pointer" @click="handleConfig">
            配置
          </div>
        </template>
      </el-input>
    </el-form-item>

    <!-- 新增精度阀值配置 -->
    <el-form-item label="精度阀值" class="!mb-4">
      <div class="w-full flex gap-2.5">
        <el-form-item prop="thresholdOperator" class="flex-shrink-0 !mb-0">
          <el-select v-model="model.thresholdOperator" placeholder="选择操作符" style="width: 120px;">
            <el-option
              v-for="option in thresholdOperatorOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="thresholdValue" class="flex-1 !mb-0">
          <el-input-number
            v-model="model.thresholdValue"
            placeholder="请输入阀值"

            :controls="false"
            class="w-full"
          />
        </el-form-item>
      </div>
    </el-form-item>

    <DialogMultiFieldCompare ref="configRef" @success="handleConfigSuccess" />
  </el-form>
</template>
