import { defineAsyncComponent } from 'vue'

// 异步组件类型
type AsyncComponentMap = Record<string, ReturnType<typeof defineAsyncComponent>>

// 动态导入所有的分析组件
const importAll = import.meta.glob('./**.vue')

export const asyncRulesWayComponents: AsyncComponentMap = {}

for (const path in importAll) {
  const name = path.split('/')[1].split('.')[0] // 从路径中获取组件名称
  asyncRulesWayComponents[name] = defineAsyncComponent(importAll[path] as any)
}
