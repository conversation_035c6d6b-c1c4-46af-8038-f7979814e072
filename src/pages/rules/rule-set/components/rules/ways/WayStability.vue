<script lang="ts" setup>
import { ruleFormConfigKey, ruleStateKey } from '~/enums/provide'

const formCommonConfig = inject(ruleFormConfigKey)!
const { data, isEdit } = inject(ruleStateKey)!

const formRef = ref()
const model = ref({
  statisticalMethod: 1, // 统计方式 1-表行数
})
const rules = ref({
  statisticalMethod: [
    { required: true, message: '请选择统计方式', trigger: 'change' },
  ],
})

function init() {
  const otherConfig = data.value.otherConfig ? JSON.parse(data.value.otherConfig) : {}
  model.value.statisticalMethod = otherConfig.statisticalMethod || 1
}

onMounted(() => {
  if (isEdit.value) {
    init()
  }
})

defineExpose({
  getData: () => {
    return {
      validateObjects: JSON.stringify([{
        otherConfig: {
          statisticalMethod: model.value.statisticalMethod,
        },
      }]),
    }
  },
  validate: () => {
    return new Promise((resolve) => {
      formRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
  },
})
</script>

<template>
  <el-form ref="formRef" :model="model" :rules="rules" v-bind="formCommonConfig">
    <el-form-item label="统计方式" prop="statisticalMethod">
      <CustomSelectV2 v-model="model.statisticalMethod" option-key="statisticType" />
    </el-form-item>
  </el-form>
</template>
