<script lang="ts" setup>
import { ruleFormConfigKey, ruleStateKey } from '~/enums/provide'
import { type MantisProject, mantisService, type MantisUser } from '~/services/mantis'

const formCommonConfig = inject(ruleFormConfigKey)!
const { data } = inject(ruleStateKey)!

const formRef = ref()
const model = ref<{
  mantisProjectId: number | undefined
  mantisHandlerId: number | undefined
}>({
  mantisProjectId: undefined,
  mantisHandlerId: undefined,
})

// 项目列表
const projectList = ref<MantisProject[]>([])
// 处理人列表
const handlerList = ref<MantisUser[]>([])

// 项目过滤关键词
const projectSearchKeyword = ref('')
// 处理人过滤关键词
const handlerSearchKeyword = ref('')

// 过滤后的项目列表
const filteredProjectList = computed(() => {
  if (!projectSearchKeyword.value) {
    return projectList.value
  }
  return projectList.value.filter(item =>
    item.name.toLowerCase().includes(projectSearchKeyword.value.toLowerCase()),
  )
})

// 过滤后的处理人列表
const filteredHandlerList = computed(() => {
  if (!handlerSearchKeyword.value) {
    return handlerList.value
  }
  return handlerList.value.filter(item =>
    item.name.toLowerCase().includes(handlerSearchKeyword.value.toLowerCase()),
  )
})

onMounted(() => {
  queryProjectList()
  queryHandlerList()
})

// 获取项目列表
async function queryProjectList() {
  try {
    const result = await mantisService.getProjects()
    projectList.value = result || []
  }
  catch (error) {
    console.error('获取项目列表失败:', error)
    projectList.value = []
  }
}

// 获取处理人列表
async function queryHandlerList() {
  try {
    const result = await mantisService.getUsers()
    handlerList.value = result || []
  }
  catch (error) {
    console.error('获取处理人列表失败:', error)
    handlerList.value = []
  }
}

// 项目过滤方法
function filterProject(query: string) {
  projectSearchKeyword.value = query
}

// 处理人过滤方法
function filterHandler(query: string) {
  handlerSearchKeyword.value = query
}

// 初始化数据（用于编辑时回显）
function init() {
  if (data.value) {
    model.value.mantisProjectId = data.value.mantisProjectId
    model.value.mantisHandlerId = data.value.mantisHandlerId
  }
}

defineExpose({
  init,
  getData: () => ({
    mantisProjectId: model.value.mantisProjectId || undefined,
    mantisHandlerId: model.value.mantisHandlerId || undefined,
  }),
  validate: () => true, // Mantis配置非必填，直接返回true
})
</script>

<template>
  <el-form ref="formRef" v-bind="formCommonConfig">
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="项目">
          <el-select
            v-model="model.mantisProjectId"
            placeholder="请选择项目"
            clearable
            filterable
            :filter-method="filterProject"
          >
            <el-option
              v-for="item in filteredProjectList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="处理人">
          <el-select
            v-model="model.mantisHandlerId"
            placeholder="请选择处理人"
            clearable
            filterable
            :filter-method="filterHandler"
          >
            <el-option
              v-for="item in filteredHandlerList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
