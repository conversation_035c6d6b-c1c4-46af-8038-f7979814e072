<script lang="ts" setup>
import CodeEditor from '~/components/CodeEditor.vue'

const codeEditorRef = ref()

const filter = defineModel<boolean>({ required: true })
const sql = defineModel<string>('sql', { default: '' })

function handleChange(value: boolean) {
  if (!value)
    sql.value = ''
}
</script>

<template>
  <div class="w-full">
    <el-switch v-model="filter" @change="handleChange" />
    <CodeEditor v-if="filter" ref="codeEditorRef" v-model="sql" show-format style="min-height: 150px; width: 100%" />
  </div>
</template>
