<script lang="ts" setup>
import { getStateFromFields } from '~/components/DynamicForm'
import { ruleFormConfigKey, ruleStateKey } from '~/enums/provide'

const formCommonConfig = inject(ruleFormConfigKey)!
const { data } = inject(ruleStateKey)!
const formRef = ref()
const fields = ref({
  ruleName: {
    required: true,
    type: 'input',
    label: '规则名称',
  },
  ruleStrength: {
    required: true,
    label: '规则强度',
    type: 'radio',
    bindings: {
      optionKey: 'ruleStrength',
    },
  },
  remark: {
    label: '规则描述',
    type: 'input',
    bindings: {
      type: 'textarea',
    },
  },
})
const model = ref<Record<string, any>>({ ...getStateFromFields(fields.value), ruleStrength: '1' })

function init() {
  Object.keys(fields.value).forEach((key) => {
    model.value[key] = data.value[key]
  })
}
defineExpose({
  init,
  getData: () => model.value,
  validate: () => formRef.value.validate(),
})
</script>

<template>
  <DynamicForm ref="formRef" v-model="model" :fields="fields" :use-grid="false" v-bind="formCommonConfig" />
</template>
