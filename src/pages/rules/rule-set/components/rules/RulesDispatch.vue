<script lang="ts" setup>
import { DICTS } from '~/enums/dict'
import { ruleFormConfigKey, ruleStateKey } from '~/enums/provide'
import { ruleScheduleServices } from '~/services/rule'
import ScheduleEdit from '../../schedule/detail.component.vue'

const formCommonConfig = inject(ruleFormConfigKey)!
const { data } = inject(ruleStateKey)!

const scheduleEditRef = ref()
const ruleSetId = computed(() => useRulesStore().currentRuleSetId!)
const model = ref<{
  scheduleId: number | undefined
}>({
  scheduleId: undefined,
})
const scheduleList = ref<{ id: number, scheduleName: string, scheduleTypeName: string }[]>([])

onMounted(() => {
  queryScheduleList()
})

async function queryScheduleList() {
  try {
    const result = await ruleScheduleServices.list({ rulesetId: ruleSetId.value })
    scheduleList.value = result.map((item) => {
      const { createAt, createBy, createByName, updateAt, updateBy, updateByName, ...rest } = item
      return {
        ...rest,
        scheduleTypeName: DICTS.scheduleType.find(one => one.value === item.scheduleType)?.label,
      }
    })
  }
  catch (e) {
    console.error(e)
    scheduleList.value = []
  }
}

function init() {
  model.value.scheduleId = data.value.ruleScheduleLinks?.[0]?.scheduleId
}

function handleCreate() {
  scheduleEditRef.value?.open()
}

defineExpose({
  init,
  getData: () => ({
    ruleScheduleIds: model.value.scheduleId ? [model.value.scheduleId] : null,
  }),
  validate: () => true,
})
</script>

<template>
  <el-form v-bind="formCommonConfig">
    <el-form-item label="调度方式" prop="">
      <el-select v-model="model.scheduleId">
        <el-option v-for="item in scheduleList" :key="item.id" :label="item.scheduleName" :value="item.id">
          <span>{{ item.scheduleName }}</span>
          <span class="ml-2 text-xs color-desc">({{ item.scheduleTypeName }})</span>
        </el-option>
      </el-select>
      <div class="ml-2.5 color-desc">
        如无可用调度，可<span class="color-primary" @click="handleCreate">新建调度</span>，如暂未决定调度方式，可创建后再配置
      </div>
    </el-form-item>
  </el-form>
  <ScheduleEdit ref="scheduleEditRef" @success="queryScheduleList" />
</template>
