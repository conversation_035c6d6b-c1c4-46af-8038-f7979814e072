<script lang="ts" setup>
import CustomRadio from '~/components/DynamicForm/components/CustomRadio.vue'
import { ruleConfigStateKey, ruleFormConfigKey, ruleStateKey } from '~/enums/provide'
import { isValidNumber, parse, stringify } from '~/utils/type'
import RuleTemplateCascader from '../RuleTemplateCascader.vue'
import { asyncRulesWayComponents } from './ways'

const formCommonConfig = inject(ruleFormConfigKey)!
const { data } = inject(ruleStateKey)!

const formRef = ref()
const formState = reactive<{
  configMethod: 1 | 2
  templateId: number | null
  ruleType: number | null
  template: number[]
  filterSwitch: boolean
  filter: string
  metric: string
  operator: string
  value: number | undefined
}>({
  configMethod: 1,
  templateId: null,
  ruleType: null,
  template: [],
  filterSwitch: false,
  filter: '',
  metric: '',
  operator: '',
  value: 0,
})

provide(ruleConfigStateKey, {
  formState,
})

const ruleConfigDetailRef = ref()
const rules = ref({
  configMethod: [{ required: true, message: '请选择配置方式', trigger: 'change' }],
  template: [{ required: true, message: '请选择规则模版', trigger: 'change' }],
  ruleType: [{ required: true, message: '请选择规则类型', trigger: 'change' }],
  metric: [{ required: true, validator: validateConditionValidator, trigger: 'change' }],
  operator: [{ required: true, validator: validateConditionValidator, trigger: 'change' }],
  value: [{ required: true, validator: validateConditionValidator, trigger: 'blur' }],
})
const componentWayMaps: Record<number, string> = {
  300: 'WayField',
  100: 'WayField',
  200: 'WayField',
  400: 'WayType',
  500: 'WayType',
  600: 'WayMulti',
  700: 'WayStability',
}
const componentOfWay = shallowRef(formState.templateId ? asyncRulesWayComponents[componentWayMaps[formState.templateId]] : null)
function validateForm() {
  return new Promise((resolve) => {
    formRef.value.validate((valid) => {
      resolve(valid)
    })
  })
}

// 校验规则
function validateConditionValidator(rule, value, callback) {
  if (!formState.metric)
    return callback(new Error('请选择校验指标'))

  if (!formState.operator)
    return callback(new Error('请选择校验符号'))

  if (!isValidNumber(formState.value))
    return callback(new Error('校验值必须为数字'))

  callback()
}

function handleChange(value) {
  if (value && value.length === 2) {
    formState.templateId = value[1]
    formState.ruleType = value[0]
  }
  else {
    formState.templateId = null
    formState.ruleType = null
  }
  setComponentOfWay()
  // TODO 切换不同模版后的初始化问题
}

async function init() {
  formState.configMethod = data.value.configMethod
  formState.templateId = data.value.templateId
  formState.ruleType = data.value.ruleType
  if (isValidNumber(formState.ruleType) && isValidNumber(formState.templateId))
    formState.template = [formState.ruleType as number, formState.templateId as number]

  // 规则校验条件
  const condition = data.value.validateCondition ? parse(data.value.validateCondition) : {}
  formState.filterSwitch = !!data.value.filter
  formState.filter = data.value.filter
  formState.metric = condition.metric
  formState.operator = condition.operator
  formState.value = condition.value

  setComponentOfWay()
  await nextTick()
}

function setComponentOfWay() {
  if (!isValidNumber(formState.ruleType))
    return
  componentOfWay.value = asyncRulesWayComponents[componentWayMaps[formState.templateId as number]]
}

defineExpose({
  init,
  getData: () => {
    const { filter, filterSwitch, value, metric, operator } = formState

    return {
      configMethod: formState.configMethod,
      templateId: formState.templateId,
      ruleType: formState.ruleType,
      filter: filterSwitch ? filter : '', // 数据过滤条件
      validateCondition: stringify({
        value,
        metric,
        operator,
      }), // 规则校验
      ...ruleConfigDetailRef.value?.getData(),
    }
  },
  validate: async () => {
    return await validateForm() && await ruleConfigDetailRef.value?.validate()
  },
  // 监控对象变更
  onWatchIdChange: () => {
    console.log('🟡 ~ ruleConfigDetailRef.value:', ruleConfigDetailRef.value)

    ruleConfigDetailRef.value?.onWatchIdChange?.()
  },
})
</script>

<template>
  <el-form ref="formRef" :model="formState" :rules="rules" v-bind="formCommonConfig">
    <el-form-item label="配置方式" prop="configMethod">
      <CustomRadio v-model="formState.configMethod" option-key="ruleConfigWay" />
    </el-form-item>
    <el-form-item label="规则模版" prop="template">
      <RuleTemplateCascader v-model="formState.template" @change="handleChange" />
    </el-form-item>
    <el-form-item label="规则类型" prop="ruleType">
      <CustomSelectV2 v-model="formState.ruleType" option-key="ruleType" disabled />
    </el-form-item>
    <div v-if="formState.ruleType" class="bg-slate-50" ml-5 mr-6 px-4 pb-4 pt-6>
      <component :is="componentOfWay" ref="ruleConfigDetailRef" :template-id="formState.templateId" />
      <el-form-item label="数据过滤">
        <RulesFilter v-model="formState.filterSwitch" v-model:sql="formState.filter" />
      </el-form-item>
      <el-form-item label="规则校验" prop="metric">
        <div class="flex gap-2">
          <CustomSelectV2 v-model="formState.metric" class="w-34!" option-key="ruleValidateMetric" />
          -
          <CustomSelectV2 v-model="formState.operator" class="w-30!" option-key="calcSymbol" />
          <el-input-number v-model="formState.value" class="w-30!" />
        </div>
      </el-form-item>
    </div>
  </el-form>
</template>
