<script lang="ts" setup>
import { getStateFromFields } from '~/components/DynamicForm'
import { ruleFormConfigKey, ruleStateKey } from '~/enums/provide'

const formCommonConfig = inject(ruleFormConfigKey)!
const { data } = inject(ruleStateKey)!

const formRef = ref()
const fields = ref({
  archiveMode: {
    required: true,
    label: '异常归档',
    type: 'switch',
    colSpan: 24,
    bindings: {
      activeValue: 1,
      inactiveValue: 2,
    },
  },
  scoreType: {
    required: true,
    label: '计分方式',
    type: 'radio',
    colSpan: 12,
    bindings: {
      optionKey: 'scoreType',
    },
  },
  qualityScore: {
    label: '质量分权重',
    type: 'inputNumber',
    colSpan: 12,
  },
})
const model = ref<Record<string, any>>({ ...getStateFromFields(fields.value), scoreType: 1, archiveMode: 2 })

function init() {
  Object.keys(fields.value).forEach((key) => {
    model.value[key] = data?.value?.[key]
  })
  if (!model.value.scoreType)
    model.value.scoreType = '1'
  if (!model.value.archiveMode)
    model.value.archiveMode = 2
}

defineExpose({
  init,
  getData: () => model.value,
  validate: () => formRef.value?.validate(),
})
</script>

<template>
  <DynamicForm ref="formRef" v-model="model" class="w-2/3" :fields="fields" v-bind="formCommonConfig" />
</template>
