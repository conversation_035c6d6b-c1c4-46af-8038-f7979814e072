<script lang="ts" setup>
import { getStateFromFields } from '~/components/DynamicForm'
import { ruleFormConfigKey, ruleStateKey } from '~/enums/provide'

const formCommonConfig = inject(ruleFormConfigKey)!
const { data, getParentRef } = inject(ruleStateKey)!

const formRef = ref()
const fields = ref({
  watchType: {
    required: true,
    label: '监控类型',
    type: 'radio',
    bindings: {
      optionKey: 'monitorType',
    },
  },
  watchId: {
    label: '对象名称',
    type: 'asyncSelect',
    required: true,
    bindings: {
      optionKey: 'dataSetSelectList',
      immediate: true,
      autoInitLabel: true,
      onChange: () => {
        getParentRef('ruleConfig')?.onWatchIdChange?.()
      },
    },
  },
})
const model = ref<Record<string, any>>({ ...getStateFromFields(fields.value), watchType: 'VIEW' })

function init() {
  Object.keys(fields.value).forEach((key) => {
    model.value[key] = data.value[key]
  })
}

defineExpose({
  init,
  getData: () => model.value,
  validate: () => formRef.value.validate(),
})
</script>

<template>
  <DynamicForm ref="formRef" v-model="model" :fields="fields" :use-grid="false" v-bind="formCommonConfig" />
</template>
