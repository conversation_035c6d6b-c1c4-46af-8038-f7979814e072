<script lang="ts" setup>
import { dataSetService } from '~/services/data-set'

interface CompareField {
  leftTableId: number
  rightTableId: number
  leftTableAlias: string
  rightTableAlias: string
  leftFieldId: string
  leftFieldName: string
  rightFieldId: string
  rightFieldName: string
}

const emit = defineEmits<{
  (e: 'success', result: CompareField[], reusltStr: string): void
}>()
const dialogRef = ref()
const formRef = ref()
const model = ref({
  leftTableId: -1,
  rightTableId: -1,
})
const leftFieldList = ref([])
const rightFieldList = ref([])

const formData = ref<{
  tableData: {
    leftField: { id: string, columnAlias: string }
    rightField: { id: string, columnAlias: string }
  }[]
}>({
  tableData: [],
})
const columns = ref([
  { label: '校验字段', prop: 'leftField', slotName: 'leftField' },
  { label: '比较字段', prop: 'rightField', slotName: 'rightField' },
])
const actions = ref([
  { name: '添加', handler: () => {
    formData.value.tableData.push({})
  } },
  { name: '删除', handler: (row: any, index: number) => {
    formData.value.tableData.splice(index, 1)
  } },
])

function open(params: { leftTableId: number, rightTableId: number }, maps: CompareField[]) {
  model.value = {
    leftTableId: params.leftTableId,
    rightTableId: params.rightTableId,
  }
  if (maps.length) {
    formData.value.tableData = maps.map(item => ({
      leftField: { id: item.leftFieldId, columnAlias: item.leftFieldName },
      rightField: { id: item.rightFieldId, columnAlias: item.rightFieldName },
    }))
  }
  else {
    formData.value.tableData = [
      { leftField: { id: '', columnAlias: '' }, rightField: { id: '', columnAlias: '' } },
    ]
  }
  dialogRef.value.open()
  init()
}

async function init() {
  Promise.all([
    queryFieldList(model.value.leftTableId),
    queryFieldList(model.value.rightTableId),
  ]).then(([_leftFieldList, _rightFieldList]) => {
    leftFieldList.value = _leftFieldList
    rightFieldList.value = _rightFieldList
  })
}

function handleSubmit() {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      if (formData.value.tableData.length === 0) {
        ElMessage.warning('请至少添加一个校验字段')
        return
      }
      const { leftTableId, rightTableId } = model.value
      const result: CompareField[] = formData.value.tableData.map((item) => {
        const { id: leftFieldId, columnAlias: leftFieldName } = item.leftField
        const { id: rightFieldId, columnAlias: rightFieldName } = item.rightField
        return {
          leftTableId,
          rightTableId,
          leftTableAlias: 'T1',
          rightTableAlias: 'T2',
          leftFieldId,
          leftFieldName,
          rightFieldId,
          rightFieldName,
        }
      })
      const resultStr = result.map(item => `${item.leftTableAlias}.\`${item.leftFieldName}\`=${item.rightTableAlias}.\`${item.rightFieldName}\``).join(' AND ')
      emit('success', result, resultStr)
      dialogRef.value.close()
    }
  })
}

async function queryFieldList(id: number) {
  try {
    if (!id)
      return []
    const result = await dataSetService.meta({ dataSetId: id })
    return result.map(item => ({
      ...item,
      label: `${item.columnName} (${item.columnAlias})`,
      value: {
        ...item,
      },
      disabled: item.dataType === 2,
    }))
  }
  catch (error) {
    console.error(error)
    return []
  }
}

defineExpose({
  open,
})
</script>

<template>
  <BaseDialog ref="dialogRef" title="多字段比较" :show-close="false" @confirm="handleSubmit">
    <el-form ref="formRef" :model="formData">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="校验表">
            <CustomAsyncSelect v-model="model.leftTableId" option-key="dataSetSelectList" disabled :immediate="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="比较表">
            <CustomAsyncSelect v-model="model.rightTableId" option-key="dataSetSelectList" disabled :immediate="true" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <BaseTable :data="formData.tableData" :columns="columns" :actions="actions" action-width="160" :show-page="false">
            <template #leftField="{ row, index }">
              <el-form-item v-if="index > -1" :prop="`tableData.${index}.leftField`" :rules="[{ required: true, message: '请选择校验字段' }]">
                <el-select-v2 v-model="row.leftField" :options="leftFieldList" filterable value-key="id" />
              </el-form-item>
            </template>
            <template #rightField="{ row, index }">
              <el-form-item v-if="index > -1" :prop="`tableData.${index}.rightField`" :rules="[{ required: true, message: '请选择比较字段' }]">
                <el-select-v2 v-model="row.rightField" :options="rightFieldList" filterable value-key="id" />
              </el-form-item>
            </template>
          </BaseTable>
        </el-col>
      </el-row>
    </el-form>
  </BaseDialog>
</template>
