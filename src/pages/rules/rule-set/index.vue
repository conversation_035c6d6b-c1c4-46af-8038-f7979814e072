<script lang="ts" setup>
import { ruleSetServices } from '~/services/rule-set'

const router = useRouter()
const rulesStore = useRulesStore()

const tableRef = ref()
const detailRef = ref()
const runRef = ref()
const formState = ref({
  name: '',
  projectId: '',
})
const columns = ref([
  { label: '规则集名称', prop: 'name', slotName: 'name', minWidth: 180 },
  { label: '最近一次运行状态', prop: 'lastStatus', slotName: 'lastStatus' },
  { label: '校验开关', prop: 'enabled', type: 'switch' },
  { label: '有效/总规则数量', prop: 'validRuleCount', formatter: ({ row }: { row: any }) => `${row.validRuleCount}/${row.ruleCount}` },
  { label: '所属项目', prop: 'projectName' },
])
const actions = ref([
  // { name: '详情', handler: handleDetail },
  { name: '运行', handler: handleRun },
  { name: '删除', handler: handleDelete },
])
const api = reactive({
  fn: ruleSetServices.list,
  getParams: () => ({ ...formState.value }),
})

function handleCreate() {
  detailRef.value?.open('添加规则集')
}

function handleDetail({ row }) {
  router.push(`/rules/rule-set/detail/${row.id}`)
}

// 运行
function handleRun({ row }) {
  runRef.value?.open(row)
}

async function handleDelete({ row }) {
  try {
    await ruleSetServices.delete(row.id)
    query()
  }
  catch (error) {
    console.error(error)
  }
}

async function query(params?: any) {
  tableRef.value?.query(params)
}
function handleTableChange(type: string, params: any) {
  const { column, row } = params
  if (type === 'form-item' && column.property === 'enabled') {
    doSwitch(row)
  }
}

async function doSwitch(row: any) {
  try {
    await ruleSetServices.switch({ rulesetId: row.id, switchStatus: row.enabled })
    ElMessage.success('操作成功')
    query()
  }
  catch (error) {
    console.error(error)
    row.enabled = !row.enabled
  }
}

function handleSuccess<T extends { id: number }>(type: 'create' | 'edit', result: T) {
  if (type === 'create') {
    useRulesStore().currentRuleSetId = result.id
    router.push(`/rules/rule-set/rule-config/create`)
  }
  else {
    query()
  }
}

onMounted(() => {
  rulesStore.$reset()
})
</script>

<template>
  <div class="h-full flex flex-col">
    <BaseHeader>
      <div class="flex gap-2.5">
        <CustomSelectV2 v-model="formState.projectId" class="min-w-62" placeholder="请选择项目" option-key="projectList" clearable @change="query({ initPage: true })" />
        <el-input v-model="formState.name" placeholder="请输入搜索关键字" class="min-w-62" clearable @keyup.enter="query({ initPage: true })" @clear="query({ initPage: true })">
          <template #suffix>
            <i class="i-ph-magnifying-glass cursor-pointer" @click="query({ initPage: true })" />
          </template>
        </el-input>
        <el-button type="primary" @click="handleCreate">
          添加规则集
        </el-button>
      </div>
    </BaseHeader>
    <BaseContent>
      <SearchTable ref="tableRef" :columns="columns" :actions="actions" :api="api" @change="handleTableChange">
        <template #name="{ row }">
          <el-link type="primary" @click="handleDetail({ row })">
            {{ row.name }}
          </el-link>
        </template>
        <template #lastStatus="{ row }">
          <div class="flex items-center gap-1">
            <template v-if="row?.lastStatus === 'SUCCESS'">
              <i class="i-ph-check-circle-fill color-green fs-18" /> 成功
            </template>
            <template v-else-if="row?.lastStatus === 'RUNNING'">
              <i class="i-ph-clock-afternoon color-blue fs-18" /> 执行中
            </template>
            <template v-else-if="row?.lastStatus === 'FAILED'">
              <i class="i-ph-x-circle-fill color-red fs-18" /> 失败
            </template>
            <template v-else>
              <i class="i-ph-minus-circle color-gray fs-18" /> 未执行
            </template>
          </div>
        </template>
      </SearchTable>
    </BaseContent>
    <RuleSetEdit ref="detailRef" @success="handleSuccess" />
    <RuleSetRun ref="runRef" @success="query" />
  </div>
</template>
