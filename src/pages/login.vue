<script setup lang="ts">
import CryptoJS from 'crypto-js'
import { authService } from '~/services/auth'
import { useUserStore } from '~/stores/user'
import { setToken } from '~/utils/token'

const loginFormRef = ref()
const formState = reactive({
  username: '',
  password: '',
})
const isRemember = ref(false)
const loading = ref(false)

const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
  ],
})

const router = useRouter()

onMounted(() => {
  initRemember()
})
function submit() {
  loading.value = true
  loginFormRef.value.validate(async (valid: boolean) => {
    if (valid)
      await doLogin()
    loading.value = false
  })
}

function initRemember() {
  const username = localStorage.getItem('username')
  if (username) {
    formState.username = username
    formState.password = localStorage.getItem('password') as string
    isRemember.value = true
  }
}

// 记住密码切换
function doRemember() {
  if (isRemember.value) {
    const { username, password } = formState
    localStorage.setItem('username', username)
    localStorage.setItem('password', password)
  }
  else {
    localStorage.removeItem('username')
    localStorage.removeItem('password')
  }
}

async function doLogin() {
  try {
    const params = {
      username: formState.username,
      password: CryptoJS.MD5(formState.password).toString().toUpperCase(),
    }
    const { token, user } = await authService.login(params)
    doRemember()
    setToken(token)
    useUserStore().setUser(user)
    router.push('/hi/1')
  }
  catch (error) {
    console.error(error)
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="login-wrapper">
    <div class="relative max-w-[1440px] min-w-294.5 flex flex-1 items-center justify-between gap-10 px-20">
      <div class="logo-wrapper">
        <div class="login-logo" />
        <h1 class="text-xl font-bold">
          天才系统
        </h1>
      </div>
      <div class="login-main" />
      <div class="group w-115 rounded-xl bg-#fff pb-22.5 pt-11">
        <h1 class="mb-12 ml-10 text-left text-3xl color-#303133">
          欢迎登录
        </h1>
        <el-form ref="loginFormRef" mx-auto w-95 size="large" :model="formState" :rules="rules">
          <el-form-item prop="username">
            <el-input v-model="formState.username" clearable>
              <template #prefix>
                <i class="i-custom-user color-#9bc5f6 fs-24 mr-4!" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="formState.password" type="password" clearable show-password>
              <template #prefix>
                <i class="i-custom-password color-#9bc5f6 fs-24 mr-4!" />
              </template>
            </el-input>
          </el-form-item>
          <el-checkbox v-model="isRemember" label="记住密码" />
          <el-button :loading="loading" class="mt-6 w-full" type="primary" @click="submit">
            登录
          </el-button>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-wrapper {
  background: linear-gradient(139deg, #f8f9fb 0%, #a8c5ff 100%);
  @apply h-full w-full flex justify-center;
  :deep(.el-input--large) {
    --el-input-inner-height: 58px;
    font-size: 16px;
  }
  :deep(.el-form-item--large) {
    @apply mb-7.5;
  }
  :deep(.el-button--large) {
    height: 60px;
    font-size: 18px;
    font-weight: 400;
  }
  :deep(.el-input .el-input__password),
  :deep(.el-input .el-input__clear) {
    font-size: 18px;
  }
}
.logo-wrapper {
  @apply absolute left-0 top-2vh flex items-center gap-2;
}
.login-main {
  background: url('../assets/images/login-main.png') no-repeat center center;
  background-size: 100% 100%;
  width: 668px;
  height: 509px;
}
.login-logo {
  background: url('../assets/images/logo.png') no-repeat center center;
  background-size: 100% 100%;
  width: 75px;
  height: 28px;
}
</style>

<route lang="yaml">
  meta:
    title: "登录"
    layout: "main"
  alias: /
</route>
