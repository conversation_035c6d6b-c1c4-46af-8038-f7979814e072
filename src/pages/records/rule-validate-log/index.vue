<script lang="ts" setup>
import CustomDate from '~/components/DynamicForm/components/CustomDate.vue'
import { ruleSetValidateService } from '~/services/validate-log'
import { parse } from '~/utils/type'

const rulesStore = useRulesStore()

const tableRef = ref()
const detailRef = ref()
const ruleValidateLogDialogRef = ref()
const formState = ref({
  keyword: '',
  date: '',
})
const columns = ref([
  { label: '规则集名称', prop: 'rulesetName', minWidth: 180 },
  { label: '项目名称', prop: 'projectName' },
  { label: '批次号', prop: 'batchNumber', minWidth: 120 },
  { label: '异常规则数', prop: 'ruleExceptionCount' },
  { label: '执行状态', prop: 'executionStatus', slotName: 'executionStatus' },
  { label: '调度信息', prop: 'scheduleName', minWidth: 180 },
  { label: '质量负责人', prop: 'qualityOwner', formatter: ({ row }: { row: any }) => row.qualityOwner ? parse(row.qualityOwner)?.[0]?.userName : '' },
  { label: '开始时间', prop: 'startTime' },
  { label: '执行时间', prop: 'executionTime', formatter: ({ row }: { row: any }) => {
    if (!row.startTime || !row.endTime)
      return '-'
    const start = new Date(row.startTime).getTime()
    const end = new Date(row.endTime).getTime()
    if (Number.isNaN(start) || Number.isNaN(end))
      return '-'
    const diffInSeconds = Math.floor((end - start) / 1000)
    return `${diffInSeconds}秒`
  } },
])
const actions = ref([
  { name: '详情', handler: handleDetail },
  { name: '查看日志', handler: handleLog },
])
const api = reactive({
  fn: ruleSetValidateService.list,
  getParams: () => ({ ...formState.value }),
})

function handleDetail({ row }) {
  detailRef.value?.open(row)
}

function handleLog({ row }) {
  ruleValidateLogDialogRef.value?.open('batch', row)
}

async function query(params?: any) {
  tableRef.value?.query(params)
}

function handleChangeDate() {
  query({ initPage: true })
}

onMounted(() => {
  rulesStore.$reset()
})
</script>

<template>
  <div class="h-full flex flex-col">
    <BaseHeader>
      <div class="flex gap-2.5">
        <CustomDate v-model="formState.date" @change="handleChangeDate" />
        <el-input v-model="formState.keyword" placeholder="请输入规则集名称" class="min-w-62" clearable @keyup.enter="query({ initPage: true })" @clear="query({ initPage: true })">
          <template #suffix>
            <i class="i-ph-magnifying-glass cursor-pointer" @click="query({ initPage: true })" />
          </template>
        </el-input>
      </div>
    </BaseHeader>
    <BaseContent>
      <SearchTable ref="tableRef" :columns="columns" :actions="actions" :api="api">
        <template #executionStatus="{ row }">
          <div class="flex items-center gap-1">
            <template v-if="row?.executionStatus === 'success'">
              <i class="i-ph-check-circle-fill color-green fs-18" /> 成功
            </template>
            <template v-else-if="row?.executionStatus === 'running'">
              <i class="i-ph-clock-afternoon color-blue fs-18" /> 运行中
            </template>
            <template v-else-if="row?.executionStatus === 'failed'">
              <i class="i-ph-x-circle-fill color-red fs-18" /> 失败
            </template>
            <template v-else>
              <i class="i-ph-minus-circle-fill color-gray fs-18" /> 未执行
            </template>
          </div>
        </template>
      </SearchTable>
    </BaseContent>
    <RuleValidateLogDetail ref="detailRef" />
    <RuleValidateLogDialog ref="ruleValidateLogDialogRef" />
  </div>
</template>
