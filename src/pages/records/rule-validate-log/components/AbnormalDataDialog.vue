<script lang="ts" setup>
import { ruleExceptionService } from '~/services/validate-log'

const dialogRef = ref()
const columns = ref<{ label: string, prop: string }[]>([])
const state = ref<any>(null)
const tableRef = ref()
const queryStr = ref('')
const api = ref({
  fn: ruleExceptionService.listByBatchNo,
  getParams: () => ({
    batchNo: state.value?.batchNumber,
    ruleId: state.value?.ruleId,
  }),
  resultFilter: (result: any) => {
    const hasData = !!result.rowData

    if (!hasData)
      return []

    const _list = JSON.parse(result.rowData)
    columns.value = Object.keys(_list[0]).map(key => ({ label: key, prop: key }))
    return result.rowData ? JSON.parse(result.rowData) : []
  },
})

/**
 * 打开对话框并初始化表格数据
 *
 * @param params 包含批次号的对象，泛型 T 继承自一个包含 batchNo 属性的对象
 * @returns 无返回值
 */
async function open<T extends { batchNo: number }>(params: T) {
  tableRef.value?.resetData()
  queryStr.value = ''
  state.value = params
  dialogRef.value?.open()
  await nextTick()
  tableRef.value?.query({ initPage: true })
}

/**
 * 根据给定的字符串过滤表格数据
 *
 * @param str 要过滤的字符串，可选参数，默认为undefined
 */
function filterStr(str?: string) {
  tableRef.value?.filterData(str)
}

defineExpose({
  open,
})
</script>

<template>
  <BaseDialog ref="dialogRef" title="异常数据查看" :show-footer-buttons="false">
    <div class="mb-2 flex justify-end">
      <el-input v-model="queryStr" placeholder="请输入搜索关键字" class="w-62!" clearable @clear="filterStr" @keyup.enter="filterStr">
        <template #suffix>
          <i class="i-ph-magnifying-glass cursor-pointer" @click="filterStr(queryStr)" />
        </template>
      </el-input>
    </div>
    <SearchTable ref="tableRef" max-height="500" :api="api" :columns="columns" :immediate="false" :show-page="false" />
  </BaseDialog>
</template>
