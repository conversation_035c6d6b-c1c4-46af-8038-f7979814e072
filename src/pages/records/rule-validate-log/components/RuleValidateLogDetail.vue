<script lang="ts" setup>
import BaseDrawer from '~/components/BaseDrawer.vue'
import { DICTS } from '~/enums/dict'
import { ruleSetValidateService, type RuleValidateOne } from '~/services/validate-log'
import { parse } from '~/utils/type'

const drawerRef = ref()
const state = ref<RuleValidateOne | null>(null)
const data = ref<any[]>([])
const loading = ref(false)
const dialogRef = ref()
// 异常数据弹窗
const abnormalDataDialogRef = ref()
// 查看日志弹窗
const ruleValidateLogDialogRef = ref()

const descData = ref()
const descOfBasic = ref([
  { label: '校验对象', prop: 'validateObject' },
  { label: '校验对象类型', prop: 'validateType' },
  { label: '校验规则', prop: 'ruleName' },
  { label: '开始时间', prop: 'startTime' },
  { label: '结束时间', prop: 'endTime' },
  { label: '持续时间(s)', prop: 'executeTime' },
  { label: '校验状态', prop: 'validateStatus', slot: true },
])

const columns = ref([
  { label: '规则名称/ID', prop: 'ruleName', formatter: ({ row }: { row: any }) => `${row.ruleName}/${row.ruleId}` },
  { label: '校验结果', prop: 'validateStatus', slotName: 'validateStatus' },
  { label: '校验范围', prop: 'validateRange' },
  { label: '规则类型', prop: 'ruleTypeName' },
  { label: '规则模版', prop: 'ruleTemplateName' },
  { label: '校验开始时间', prop: 'startTime' },
])
const actions = ref([
  { name: '详情', handler: ({ row }: { row: any }) => {
    const _validateCondition = row.validateCondition ? parse(row.validateCondition) : ''
    descData.value = { ...row, _validateConditionStr: `${DICTS.ruleValidateMetric.find(item => item.value === _validateCondition.metric)?.label} ${_validateCondition?.operator} ${_validateCondition?.value}` }
    dialogRef.value.open()
  } },
  {
    name: '异常数据查看',
    handler: ({ row }) => {
      abnormalDataDialogRef.value.open(row)
    },
  },
  {
    name: '查看日志',
    handler: ({ row }) => {
      ruleValidateLogDialogRef.value.open('rule', row)
    },
  },
])

const resultData = ref<any[]>([])
const resultColumns = ref([
  { label: '业务日志', prop: 'validateTime' },
  { label: '指标名称', prop: 'metricName' },
  { label: '指标值', prop: 'metricValue' },
])

async function open(params: RuleValidateOne) {
  reset()

  state.value = params
  drawerRef.value.open()
  await query()
}

function reset() {
  data.value = []
}

async function query() {
  loading.value = true
  try {
    const result = await ruleSetValidateService.detail(state.value!.batchNumber)
    data.value = result || []
    resultData.value = parse(result[0]?.ruleValidateResults) || []
  }
  catch (e) {
    console.error(e)
  }
  finally {
    loading.value = false
  }
}

function close() {
  drawerRef.value.close()
}

function closeDialog() {
  dialogRef.value.close()
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <BaseDrawer ref="drawerRef" class="w-300!" :title="state?.rulesetName" :show-footer-buttons="false">
    <BaseTable :data="data" :columns="columns" :actions="actions" :show-page="false" :loading="loading">
      <template #validateStatus="{ row }">
        <div class="flex items-center gap-1">
          <template v-if="!row?.validateStatus">
            <i class="i-ph-check-circle-fill color-green fs-18" /> 通过
          </template>
          <template v-else>
            <i class="i-ph-x-circle-fill color-red fs-18" /> 不通过
          </template>
        </div>
      </template>
    </BaseTable>
  </BaseDrawer>
  <BaseDialog ref="dialogRef" title="校验详情" @confirm="closeDialog">
    <el-descriptions title="基本信息" :column="2">
      <template v-for="item in descOfBasic" :key="item.prop">
        <el-descriptions-item :label="item.label" label-align="right" label-class-name="w-24 inline-flex justify-end">
          <template v-if="item.slot">
            <template v-if="item.prop === 'validateStatus'">
              <template v-if="!descData?.validateStatus">
                <i class="i-ph-check-circle-fill relative mt-0.5 color-green fs-18" /> 校验通过
              </template>
              <template v-else>
                <i class="i-ph-x-circle-fill relative mt-0.5 color-red fs-18" /> 校验不通过
              </template>
            </template>
          </template>
          <template v-else>
            {{ descData?.[item.prop] }}
          </template>
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <el-descriptions title="校验配置" :column="1">
      <el-descriptions-item label="规则校验" label-align="right" label-class-name="w-24 inline-flex justify-end">
        {{ descData?._validateConditionStr }}
      </el-descriptions-item>
    </el-descriptions>
    <h3 class="el-descriptions__title">
      校验结果
    </h3>
    <BaseTable :columns="resultColumns" :data="resultData" :show-page="false" />
  </BaseDialog>
  <AbnormalDataDialog ref="abnormalDataDialogRef" />
  <RuleValidateLogDialog ref="ruleValidateLogDialogRef" />
</template>
