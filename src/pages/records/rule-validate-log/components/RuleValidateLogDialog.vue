<script lang="ts" setup>
import { ruleExceptionService } from '~/services/validate-log'

const visible = ref(false)
const loading = ref(false) // 侧边加载loading
const logLoading = ref(false) // 日志加载loading
const state = reactive<{
  code: string // 日志内容
  logs: string[] // 日志列表
  active: string // 当前选中的日志
}>({
  code: ``,
  logs: [],
  active: '',
})

const activeBatchId = computed(() => getBatchKey(state.active)) // 当前选中的批次号
const activeRuleId = computed(() => getRuleKey(state.active)) // 当前选中的规则号

// 获取批次号
function getBatchKey(str: string) {
  return isRuleLogName(str) ? str.split('.')[0].split('-')[0] : str.split('.')[0]
}

// 获取规则号
function getRuleKey(str: string) {
  if (!isRuleLogName(str))
    return null
  return str.split('.')[0].split('-')[1]
}

// 判断是否为规则日志名称
function isRuleLogName(logName: string) {
  return logName.includes('-')
}

// 重置状态
function reset() {
  state.code = ''
  state.logs = []
}

// 打开日志弹窗，type: batch批次号, rule包含规则号
async function open<T extends { batchNumber: string, ruleId?: string }>(type: 'batch' | 'rule', params: T) {
  reset()
  visible.value = true
  loading.value = true
  await querySideList(params.batchNumber)
}

// 切换日志
function handleSelect(log: string) {
  // 防止重复点击同一个日志
  if (state.active === log)
    return
  state.active = log
  // 如果有规则号，则查询规则号日志，否则查询批次号日志
  activeRuleId.value ? queryLogByBatchAndRule(activeBatchId.value, activeRuleId.value) : queryLogByBatch(activeBatchId.value)
}

// 请求侧边列表
async function querySideList(batchNumber: string) {
  try {
    const res = await ruleExceptionService.queryRuleLogSideList(batchNumber)
    state.logs = res || []
    if (state.logs.length > 0) {
      state.active = state.logs[0]
      await queryLogByBatch(activeBatchId.value)
    }
  }
  catch (error) {
    console.error(error)
  }
  finally {
    loading.value = false
  }
}

// 获取批次号规则日志
async function queryLogByBatch(batchNumber: string) {
  try {
    logLoading.value = true
    const res = await ruleExceptionService.queryBatchLogContent(batchNumber)
    state.code = res
  }
  catch (error) {
    console.error(error)
  }
  finally {
    logLoading.value = false
  }
}

// 获取批次号+规则号日志
async function queryLogByBatchAndRule(batchNumber: string, ruleId: string) {
  try {
    logLoading.value = true
    const res = await ruleExceptionService.queryRuleLogContent(batchNumber, ruleId)
    state.code = res
  }
  catch (error) {
    console.error(error)
  }
  finally {
    logLoading.value = false
  }
}

defineExpose({
  open,
})
</script>

<template>
  <el-dialog v-model="visible" fullscreen title="运行日志" class="custom-full-dialog" width="600px">
    <div v-loading="loading || logLoading" class="h-full flex overflow-hidden border-1 border-gray-200 border-solid">
      <div class="min-w-60 flex-[0_0_15%] border-r-1 border-gray-200 border-solid">
        <ul class="h-full overflow-auto">
          <li
            v-for="log in state.logs" :key="log"
            :class="{ 'bg-primary-lighter': log === state.active }"
            class="w-full overflow-hidden text-ellipsis whitespace-nowrap border-b-1 border-gray-200 border-solid px-2 py-1 leading-loose"
            @click="handleSelect(log)"
          >
            {{ log }}
          </li>
        </ul>
      </div>
      <div class="flex flex-1 flex-col overflow-hidden">
        <CodeEditor v-model="state.code" style="min-height: 30vh;" disabled class="flex-1" />
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss">
.custom-full-dialog.el-dialog {
  @apply flex flex-col overflow-hidden!;
  .el-dialog__body {
    @apply flex-1 overflow-hidden!;
  }
}
</style>
