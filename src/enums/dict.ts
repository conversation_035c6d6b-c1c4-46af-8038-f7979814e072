import { authService } from '~/services/auth'
import { dataSetService } from '~/services/data-set'
import { dataSourceService } from '~/services/data-source'
import { projectService } from '~/services/project'
import { CoverageTypeEnum, PartitionExpressionFromEnum } from '~/types/enum'

export const DICTS = {
  yesOrNo: [
    { label: '是', value: 'yes' },
    { label: '否', value: 'no' },
  ],
  // 字段类型
  dataType: [
    { label: '字符串', value: 1 },
    { label: '数字', value: 2 },
    { label: '日期', value: 3 },
  ],
  // 值域类型
  dataValueType: [
    { label: '文本', value: 'text' },
    { label: '数字', value: 'number' },
    { label: '日期', value: 'date' },
  ],
  // 规则强度
  ruleStrength: [
    { label: '弱规则', value: '1' },
    { label: '强规则', value: '2' },
  ],
  // 监控类型
  monitorType: [
    { label: '数据集', value: 'VIEW' },
    // { label: '表', value: 'TABLE:' }, // 暂时不开放
  ],
  // 规则类型
  ruleType: [
    { label: '一致性', value: 1 },
    { label: '有效性', value: 2 },
    // { label: '及时性', value: 3 },
    { label: '唯一性', value: 4 },
    { label: '完整性', value: 5 },
    { label: '稳定性', value: 6 },
  ],
  // 规则配置-配置方式
  ruleConfigWay: [
    { label: '模版创建', value: 1 },
    // { label: '自定义SQL', value: 2 }, // 暂时不开放
  ],
  // 规则校验方式
  ruleValidateMetric: [
    // { label: '正常行数', value: 'NORMAL_NUMBER' },
    // { label: '正常率(%)', value: 'NORMAL_RATE' },
    { label: '异常行数', value: 'ERROR_NUMBER' },
    // { label: '异常率(%)', value: 'ERROR_RATE' },
    { label: '统计值', value: 'STATISTICS' }, // 可以做成某些模版情况下展示
  ],
  // 计算符号
  calcSymbol: [
    { label: '>', value: '>' },
    { label: '>=', value: '>=' },
    { label: '<', value: '<' },
    { label: '<=', value: '<=' },
    { label: '=', value: '=' },
    { label: '!=', value: '!=' },
  ],
  // 计分方式
  scoreType: [
    { label: '质量校验状态', value: 1 },
    { label: '数据合格比例', value: 2 },
  ],
  // 调度类型
  scheduleType: [
    { label: '定时调度', value: 'PERIOD_SCHEDULE' },
    { label: '补偿调度', value: 'COMPENSATE_SCHEDULE' },
  ],
  // 枚举方式
  enumType: [
    { label: 'in', value: 'in' },
    { label: 'not in', value: 'not in' },
  ],
  // 关联方式
  joinType: [
    { label: 'full join', value: 'full join' },
    { label: 'left join', value: 'left join' },
    { label: 'right join', value: 'right join' },
    { label: 'inner join', value: 'inner join' },
  ],
  // 统计方式
  statisticType: [
    { label: '表行数', value: 1 },
  ],
  // 试跑范围
  partitionExpressionFromList: [
    { label: '已有调度', value: PartitionExpressionFromEnum.SCHEDULE },
    { label: '自定义校验范围', value: PartitionExpressionFromEnum.CUSTOM },
  ],
  // 自定义校验范围
  customPartitionExpression: [
    { label: 'full table', value: 'full table' },
  ],
  // 试跑状态
  runStatus: [
    { label: '未执行', value: 0 },
    { label: '成功', value: 1 },
    { label: '失败', value: 2 },
    { label: '执行中', value: 3 },
  ],
  // 告警方式
  alertType: [
    { label: '短信', value: 'SMS' },
    { label: '邮件', value: 'EMAIL' },
  ],
  // 规则覆盖方式
  coverageType: [
    { label: '所有规则', value: CoverageTypeEnum.ALL },
    { label: '所有强规则', value: CoverageTypeEnum.STRONG },
    { label: '所有弱规则', value: CoverageTypeEnum.WEAK },
    { label: '自定义', value: CoverageTypeEnum.CUSTOM },

  ],
}

export type LocalDictKey = keyof typeof DICTS

export interface FetchConfig {
  fn: (params?: any) => Promise<any>
  props?: {
    label?: string
    value?: string
    options?: string
    desc?: string
  }
  resultFilter?: (res: any) => any
}

const userList: FetchConfig = {
  fn: authService.userList,
  props: {
    label: 'userFormat',
    value: 'userId',
  },
  resultFilter: (result) => {
    return result.map((user: any) => ({
      ...user,
      options: user,
      userFormat: `${user.userName}（${user.userId}）`,
    }))
  },
}

const projectList = {
  fn: projectService.selectList,
  props: {
    label: 'name',
    value: 'id',
  },
}

const dataSourceList = {
  fn: dataSourceService.selectList,
  props: {
    label: 'name',
    value: 'id',
  },
}

const dataSetGroup: FetchConfig = {
  fn: dataSetService.treeCatalog,
  resultFilter: (result) => {
    return [{
      label: '根节点',
      value: 0,
      children: result.map(i => ({ ...i, label: i.name, value: i.id })),
    }]
  },
}

export const FETCH_DICT: Map<string, FetchConfig> = new Map([
  // 数据集分组下拉列表
  ['dataSetGroup', dataSetGroup],
  // 数据源下拉列表
  ['dataSourceList', dataSourceList],
  // 项目下拉列表
  ['projectList', projectList],
  // 用户下拉列表
  ['userList', userList],
])

type KeyType<T> = T extends Map<infer K, any> ? K : never
export type FetchDictKey = KeyType<typeof FETCH_DICT>
