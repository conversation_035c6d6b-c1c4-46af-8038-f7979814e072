export type Menu = Array<MenuItem>

export interface MenuItem {
  path: string
  title: string
  icon?: string
  hidden?: boolean
  children?: MenuItem[]
  paramsStorageKey?: string
  currentRoute?: string
}
// 菜单配置
export const MENUS: Menu = [
  { title: '规则管理', path: '/rules', icon: 'i-custom-project', children: [
    { title: '项目管理', path: '/rules/project' },
    { title: '数据集管理', path: '/rules/data-set' },
    { title: '规则集管理', path: '/rules/rule-set', children: [
      { title: '规则集详情', path: '/rules/rule-set/detail/[id]', hidden: true, paramsStorageKey: 'ruleset_detail_id', children: [
        { title: '规则新增', path: '/rules/rule-set/rule-config/create', hidden: true },
        { title: '规则编辑', path: '/rules/rule-set/rule-config/edit/[id]', hidden: true },
      ] },
    ] },
  ] },
  { title: '校验记录', path: '/records/rule-validate-log', icon: 'i-custom-project' },
  { title: '调度管理', path: '/schedule', icon: 'i-custom-project' },
  { title: '数据源管理', path: '/data-source', icon: 'i-custom-project' },
]
