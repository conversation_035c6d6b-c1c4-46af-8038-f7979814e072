import type { Reactive } from 'vue'
import type { RuleDetailModule } from '~/types/rules'

// 规则配置表单项宽度配置key
export const ruleFormConfigKey = Symbol('ruleLabelWidth')
export const ruleStateKey: InjectionKey<{
  getParentRef: (key: RuleDetailModule) => any
  data: Ref<Record<string, any>>
  isEdit: ComputedRef<boolean>
}> = Symbol('ruleStateKey')

// 规则配置的外部表单
export const ruleConfigStateKey: InjectionKey<{
  formState: Reactive<Record<string, any>>
}> = Symbol('ruleConfigStateKey')
