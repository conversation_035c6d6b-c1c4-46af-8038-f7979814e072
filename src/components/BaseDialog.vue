<script lang="ts" setup>
withDefaults(defineProps<{
  form?: boolean
  confirmLoading?: boolean
  confirmText?: string
  showFooterButtons?: boolean
}>(), {
  form: true,
  confirmLoading: false,
  confirmText: '确定',
  showFooterButtons: true,
})

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'cancel'): void
  (e: 'confirm'): void
}>()

const visible = ref(false)
const state = reactive({
  title: '',
})
function open({ title } = { title: '' }) {
  state.title = title
  visible.value = true
}

function handleClose() {
  visible.value = false
}

function handleCancel() {
  emit('cancel')
  handleClose()
}

function handleConfirm() {
  emit('confirm')
}

defineExpose({
  open,
  close: handleClose,
})
</script>

<template>
  <el-dialog v-model="visible" width="960px" :title="state.title" v-bind="$attrs" append-to-body :close-on-click-modal="false" :close-on-press-escape="false">
    <div class="mx-5" :class="form ? 'pt-5' : ''">
      <slot />
    </div>
    <template #footer>
      <slot v-if="$slots.footer" name="footer" />
      <div v-else-if="showFooterButtons" class="flex" :class="$slots['footer-before'] ? 'justify-between' : 'justify-end'">
        <slot name="footer-before" />
        <div class="flex justify-end">
          <el-button w-22 @click="handleCancel">
            取消
          </el-button>
          <el-button min-w-22 type="primary" :loading="confirmLoading" @click="handleConfirm">
            {{ confirmText }}
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
