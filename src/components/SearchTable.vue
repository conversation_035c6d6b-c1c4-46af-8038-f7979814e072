<script lang="ts" setup>
import type { TableProps } from './BaseTable/index.vue'

interface SearchTableProps extends TableProps {
  api: {
    fn: (params?: any) => Promise<any>
    getParams?: () => any
    resultFilter?: (res: any) => any[]
  }
  immediate?: boolean // 是否立即加载数据
  showPage?: boolean // 是否显示分页
}

const props = withDefaults(defineProps<SearchTableProps>(), {
  immediate: true,
  showPage: true,
})

const tableRef = ref()
const loading = ref(false)
const data = ref([])
const total = ref(0)
const filteredData = ref<any[] | null>(null)

const pageState = reactive({
  pageNum: 1,
  pageSize: 10,
  page: '',
})

const showData = computed(() => {
  return filteredData.value || data.value
})

async function query({ initPage } = { initPage: false }) {
  loading.value = true
  if (initPage) {
    tableRef.value?.pageReset({ noEmit: true })
    pageState.pageNum = 1 // 从内部重置会多出发一次搜索
    await nextTick()
  }
  filteredData.value = null
  try {
    const params = {
      ...(props.showPage ? pageState : {}),
      ...(props.api.getParams?.() || {}),
    }
    const res = await props.api.fn(params)
    data.value = props.api.resultFilter ? props.api.resultFilter(res) : res.list
    total.value = res.total
  }
  catch (e) {
    console.error(e)
  }
  finally {
    loading.value = false
  }
}

async function handlePageChange({ page, pageSize }: { page: number, pageSize: number }) {
  pageState.pageNum = page
  pageState.pageSize = pageSize
  await query()
}

onMounted(() => {
  if (props.immediate) {
    query()
  }
})

/**
 * 重置数据函数
 *
 * 将 data.value 重置为一个空数组
 */
function resetData() {
  filteredData.value = null
  data.value = []
}

/**
 * 过滤数据函数
 *
 * @param queryStr 过滤条件字符串，默认为null
 */
function filterData(queryStr = null) {
  if (!queryStr)
    return filteredData.value = null

  filteredData.value = data.value.filter((item) => {
    return Object.values(item).some(val =>
      String(val).toLowerCase().includes((queryStr as string).toLowerCase()),
    )
  })
}

defineExpose({
  query,
  resetData,
  filterData,
})
</script>

<template>
  <BaseTable ref="tableRef" :loading="loading" :columns="columns" :actions="actions" :total="total" :data="showData" :show-page="showPage" v-bind="$attrs" @page-change="handlePageChange">
    <template
      v-for="item in columns.filter(i => i.slotName)"
      :key="item.prop"
      #[item.slotName]="{ row, prop }"
    >
      <slot
        :name="item.slotName"
        :row="row"
        :prop="prop"
      />
    </template>
  </BaseTable>
</template>
