<script lang="ts" setup>
import type { DescribeConfigOne } from '~/types/common'

defineProps<{
  data: any
  titleKey: any // title key
  icon?: string // icon class name
  descConfig: DescribeConfigOne[] // 描述信息配置
}>()

const emit = defineEmits<{
  (e: 'edit'): void
  (e: 'delete'): void
}>()
</script>

<template>
  <div class="border-1 border-#EBEDF0 rounded-xl border-solid bg-white p-5">
    <div class="mb-5.75 flex items-center justify-between gap-2">
      <div w-full flex items-center gap-2 overflow-hidden>
        <i v-if="icon" :class="icon" class="is-origin" />
        <h5 overflow-hidden text-ellipsis whitespace-nowrap text-sm font-500 :title="data[titleKey]">
          {{ data[titleKey] }}
        </h5>
      </div>
      <div class="flex items-center gap-2">
        <i class="i-ph-note-pencil cursor-pointer color-neutral-400" @click="emit('edit')" />
        <i class="i-ph-trash cursor-pointer color-neutral-400" @click="emit('delete')" />
      </div>
    </div>
    <ul>
      <li v-for="one in descConfig" :key="one.key" class="mb-2.5 flex items-start overflow-hidden text-xs last:mb-0">
        <div class="whitespace-nowrap color-#a8aaaf">
          {{ one.name }}：
        </div>
        <slot v-if="one.slotName" :key="one.key" :name="one.slotName" :value="data[one.key]" />
        <div v-else class="overflow-hidden text-ellipsis whitespace-nowrap color-#525353" :title="data[one.key]" v-html="one.format ? one.format(data[one.key]) : data[one.key]" />
      </li>
    </ul>
  </div>
</template>
