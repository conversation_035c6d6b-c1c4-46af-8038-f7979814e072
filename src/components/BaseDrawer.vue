<script lang="ts" setup>
withDefaults(defineProps<{
  confirmLoading?: boolean
  confirmButtonText?: string
  cancelButtonText?: string
  title?: string
  showFooterButtons?: boolean
}>(), {
  title: '',
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  showFooterButtons: true,
})

const emit = defineEmits<{
  (e: 'confirm', done: () => void): void
  (e: 'cancel'): void
}>()

const visible = ref(false)
const direction = ref('rtl')
function cancelClick() {
  handleClose()
  emit('cancel')
}

function handleClose() {
  visible.value = false
}

function confirmClick() {
  emit('confirm', handleClose)
}

defineExpose({
  open: () => {
    visible.value = true
  },
  close: handleClose,
})
</script>

<template>
  <el-drawer v-model="visible" :direction="direction" v-bind="$attrs">
    <template #header>
      <h4>{{ title }}</h4>
    </template>
    <template #default>
      <slot />
    </template>
    <template #footer>
      <div v-if="showFooterButtons" style="flex: auto">
        <el-button @click="cancelClick">
          取消
        </el-button>
        <el-button type="primary" :loading="confirmLoading" @click="confirmClick">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
