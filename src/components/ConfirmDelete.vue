<script lang="ts" setup>
const { closePopover } = useGlobalPopover()
</script>

<template>
  <div>
    <div class="flex items-center gap-2">
      <i class="i-ph-warning-circle-fill color-amber" />
      确认删除吗？
    </div>
    <div class="mt-2 flex justify-end">
      <el-button size="small" @click="closePopover">
        取消
      </el-button>
      <el-button size="small" type="primary" @click="$emit('confirm')">
        确定
      </el-button>
    </div>
  </div>
</template>
