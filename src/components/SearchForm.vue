<script lang="ts" setup>
import type { Field } from './DynamicForm/types/form'
import { getStateFromFields } from './DynamicForm'

const props = defineProps<{
  modelValue: any
  fields: Record<string, Field>
}>()

const emit = defineEmits<{
  (event: 'submit'): void
}>()

const formRef = ref()
const model = defineModel<Record<string, any>>({ required: true })

function handleReset() {
  model.value = getStateFromFields(props.fields)
}

function handleSubmit() {
  emit('submit')
}

defineExpose({
  formRef,
})
</script>

<template>
  <div class="form-card">
    <DynamicForm
      ref="formRef"
      v-bind="$attrs"
      v-model="model"
      :fields="fields"
      label-width="88px" label-position="left"
      :use-grid="false"
      class="search-form"
    />
    <div class="flex justify-end">
      <el-button @click="handleReset">
        重置
      </el-button>
      <el-button type="primary" @click="handleSubmit">
        查询
      </el-button>
    </div>
  </div>
</template>

<style lang="scss">
.search-form {
  .form-row {
    @apply flex justify-between gap-2%;
  }
  .form-col {
    @apply flex-1;
  }
}
</style>
