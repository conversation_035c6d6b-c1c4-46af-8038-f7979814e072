<script lang="ts" setup>
import type { MenuItem } from '~/enums/menu'

const props = defineProps<{ data: MenuItem }>()

const router = useRouter()

// 是否有子菜单项
const hasChildren = computed(() => props.data.children && props.data.children.filter(child => !child.hidden).length)

// 点击菜单项
function handleClickMenu<T extends { index: string }>(menu: T) {
  const { index: path } = menu
  router.push(path)
}
</script>

<template>
  <el-sub-menu v-if="hasChildren" :index="data.path">
    <template #title>
      <i class="mr-1.5 inline-flex text-xl" :class="data.icon" /><span>{{ data.title }}</span>
    </template>
    <SideMenuItem v-for="item in data.children" :key="item.path" :data="item" />
  </el-sub-menu>
  <el-menu-item v-else :index="data.path" @click="handleClickMenu">
    <template #title>
      <i class="mr-1.5 inline-flex text-xl" :class="data.icon" /><span>{{ data.title }}</span>
    </template>
  </el-menu-item>
</template>
