<script lang="ts" setup>
import { MENUS } from '~/enums/menu'
import { findMenuParentByRoute } from '~/utils/menu'

const { isCollapsed } = useCollapse()
const route = useRoute()

const activePath = computed<string>(() => {
  if (route.meta?.activePath)
    return route.meta.activePath as string

  const currentParent = findMenuParentByRoute(route, MENUS)
  if (currentParent)
    return currentParent.path as string

  return route.path
})
</script>

<template>
  <el-menu
    class="layout-menu" v-bind="$attrs"
    :collapse="isCollapsed"
    :default-active="activePath"
  >
    <SideMenuItem v-for="menu in MENUS" :key="menu.title" :data="menu" />
  </el-menu>
</template>

<style lang="scss" scoped>
.layout-menu {
  @apply b-r-0;
  :deep(.el-sub-menu.is-active .el-sub-menu__title) {
    color: var(--el-menu-active-color);
  }
  :deep(.el-menu-item.is-active) {
    background: linear-gradient(53deg, rgba(3, 193, 255, 0.25) 0%, rgba(6, 72, 255, 0.25) 100%),
      linear-gradient(to left, rgba(0, 0, 0, 0) 98%, #0575ff 98%);
  }
}
</style>
