<script lang="ts" setup>
import userPng from '~/assets/images/user.png'
import { useUserStore } from '~/stores/user'

enum Command {
  LOGOUT = 'logout', // 登出
}

const { toggleCollapse } = useCollapse()
const userStore = useUserStore()
const router = useRouter()

function handleCommand(command: Command) {
  switch (command) {
    case Command.LOGOUT:
      userStore.logout(() => router.push('/login'))
      break
  }
}
</script>

<template>
  <i class="i-ph-text-outdent cursor-pointer text-lg" @click="toggleCollapse" />
  <div class="flex items-center">
    <div v-if="userStore.user" mr-2 text-sm>
      {{ userStore.user.userName }} <span color->({{ userStore.user.userId }})</span>
    </div>

    <el-dropdown @command="handleCommand">
      <div h-11.5 w-11.5 overflow-hidden rounded-full>
        <img :src="userPng" alt="">
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item :command="Command.LOGOUT">
            退出登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<style lang="scss" scoped>

</style>
