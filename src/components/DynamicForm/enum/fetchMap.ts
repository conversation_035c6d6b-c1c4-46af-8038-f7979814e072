import { dataSetService } from '~/services/data-set'
import { dataSourceService } from '~/services/data-source'

interface FetchDetail {
  fn: (params?: any) => Promise<any> // 查询函数
  queryLabel: string // 查询参数字段值
  value: string // 查询结果value值
  label: string // 查询结果label值
}
export const fetchMaps: Map<string, FetchDetail> = new Map([
  ['dataSourceList', {
    fn: dataSourceService.listByName,
    queryLabel: 'dsName',
    value: 'id',
    label: 'name',
  }],
  // 数据集列表
  ['dataSetSelectList', {
    queryLabel: 'name',
    queryValue: 'dataSetId',
    value: 'id',
    label: 'name',
    fn: dataSetService.selectList,
  }],
])
