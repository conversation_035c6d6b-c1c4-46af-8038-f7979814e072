import { dictCache } from '~/composables/useDict'

// 缓存已经获取过的字典
export const cacheCodes = new Map()

export function useOptions(props, emit) {
  const instance = getCurrentInstance()
  const isEventRegistered = (eventName) => {
    if (!instance?.emitsOptions)
      return false
    return Object.keys(instance.emitsOptions || {}).includes(eventName)
  }

  // 选项列表
  const optionList = ref([])
  // 选项属性
  const disableOptionFn = ref(() => false)

  const initOptionsList = async () => {
    if (!props.optionKey) {
      optionList.value = props.options || []
      return
    }
    optionList.value = await dictCache.getField(props.optionKey)
    if (isEventRegistered('initedList'))
      emit('initedList', optionList.value)
  }

  const handleChange = (value) => {
    if (!props.multiple) {
      const option = optionList.value?.find(option => option[props.props.value] === value)
      emit('change', value, { option })
    }
    else {
      if (!value)
        emit('change', value, { option: [] })
      const options = []
      value.forEach((item) => {
        const option = optionList.value?.find(option => option[props.props.value] === item)
        options.push(option)
      })
      emit('change', value, { option: options })
    }
  }

  const refetchOptionData = async () => {
    dictCache.clearCache(props.optionKey)
    await initOptionsList()
  }

  onMounted(() => {
    // 必须使用这个条件，有些组件可能没有immediate选项，当不需要立即请求时，immediate设置为false
    if (props.immediate !== false)
      initOptionsList()
  })
  return {
    optionList,
    handleChange,
    disableOptionFn,
    refetchOptionData,
  }
}
