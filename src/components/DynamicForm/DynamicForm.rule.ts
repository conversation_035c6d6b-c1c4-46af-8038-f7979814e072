import type { Field } from './types/form'
import { requiredTextMap } from '~/components/DynamicForm/DynamicForm.config'
import { placeholderPrefix } from '~/components/DynamicForm/DynamicForm.utils'

const rulesMap = {
  area: setAreaDefaultRules,
}

// 根据组件类型设置默认必填规则
export function setDefaultRulesFromFieldType(field, modelValue) {
  return field.required
    ? [
        ...(rulesMap[field.type] ? rulesMap[field.type](field, modelValue) : setBaseDefaultRules(field)),
        ...(field.rules || []),
      ]
    : (field.rules || [])
}

// 设置默认（非特殊组件）必填规则
export function setBaseDefaultRules(field: Field) {
  return [
    {
      required: true,
      message: `${placeholderPrefix(field)}${field.label || ''}`,
      trigger: 'blur',
    },
  ]
}

// 设置地区组件必填规则
export function setAreaDefaultRules(field: Field, modelValue: any) {
  return [
    {
      required: true,
      message: `${requiredTextMap[field.type]}${field.label || ''}`,
      validator: (rule, value, callback) => {
        const isMunicipality = modelValue
        const hasProvinceAndCity = modelValue && modelValue.province && modelValue.city
        const hasArea = modelValue && modelValue.district

        if ((isMunicipality && hasProvinceAndCity) || (!isMunicipality && hasProvinceAndCity && hasArea))
          callback()
        else
          callback(new Error('请选择'))
      },
      trigger: 'blur',
    },
  ]
}
