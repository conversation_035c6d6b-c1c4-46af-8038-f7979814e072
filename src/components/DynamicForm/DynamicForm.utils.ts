import { requiredTextMap } from '~/components/DynamicForm/DynamicForm.config.js'

// 生成表单的提示语前缀
export const placeholderPrefix = field => requiredTextMap[field.type] || requiredTextMap.input

// 通过表单配置项生成一组默认数据
export function getStateFromFields(fieldConfig) {
  const result = {}
  Object.keys(fieldConfig).forEach((key) => {
    result[key] = fieldConfig[key].defaultValue || getDefaultValue(fieldConfig[key])
  })
  return result
  // 不同基础组件的默认值
  function getDefaultValue(field) {
    if (field.type === 'checkbox' || field.type === 'file' || (field.type === 'select' && field.bindings?.multiple))
      return []
    if (field.type === 'inputNumber')
      return null
    if (field.type === 'area') {
      return {
        province: '',
        city: '',
        district: '',
      }
    }
    return ''
  }
}
