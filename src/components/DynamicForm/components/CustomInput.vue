<script setup>
import { computed, defineEmits, defineProps } from 'vue'

const props = defineProps({
  modelValue: [String, Number],
  readonly: Boolean,
})
const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})
</script>

<template>
  <div v-if="readonly">
    {{ innerValue }}
  </div>
  <el-input v-else v-model="innerValue" clearable v-bind="$attrs" />
</template>
