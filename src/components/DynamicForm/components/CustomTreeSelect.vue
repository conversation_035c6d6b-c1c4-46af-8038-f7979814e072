<script setup>
import { optionProps } from '~/components/DynamicForm/utils/optionProps'
import { useOptions } from '../hooks/useOption'

const props = defineProps({
  modelValue: [String, Number, Array],
  ...optionProps,
  checkStrictly: {
    type: <PERSON><PERSON><PERSON>,
    default: true,
  },
  readonly: <PERSON><PERSON><PERSON>,
})
const { optionList } = useOptions(props)

const treeSelectProps = ref({
  label: props.props?.label || 'label',
  value: props.props?.value || 'value',
  children: 'children',
})

const innerValue = defineModel()
</script>

<template>
  <ElTreeSelect
    v-model="innerValue"
    :data="optionList"
    :props="treeSelectProps"
    :value-key="treeSelectProps.value"
    :check-strictly="checkStrictly"
    :render-after-expand="false"
    v-bind="$attrs"
  />
</template>

<style>
.el-select__wrapper.is-disabled {
  background: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
  color: #000;
  cursor: text;
  padding-left: 0;

  .el-select__suffix {
    display: none;
  }

  .el-select__placeholder {
    color: #000;
  }
}
</style>
