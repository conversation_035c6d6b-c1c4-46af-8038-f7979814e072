<script lang="ts" setup>
import { computed, defineEmits, defineProps } from 'vue'
import { useOptions } from '../hooks/useOption'

interface SelectProps {
  modelValue: any
  props?: {
    label?: string
    value?: string
  }
  optionKey?: string
  multiple?: boolean
  disableOptionMethod?: (option: any) => boolean
  apiParams?: any
  filterMethod?: (option: any) => boolean
  immediate?: boolean
  options?: any[]
}

const props = withDefaults(defineProps<SelectProps>(), {
  props: () => ({
    label: 'label',
    value: 'value',
  }),
  immediate: true,
})

const emit = defineEmits(['update:modelValue', 'change', 'initedList'])

const { optionList, handleChange, disableOptionFn, refetchOptionData } = useOptions(props, emit)
const innerValue = defineModel({ required: true })

const disableOptionFnCalc = computed(() => {
  return props.disableOptionMethod || disableOptionFn.value
})

const filteredList = computed(() => {
  if (props.filterMethod) {
    return optionList.value.filter(props.filterMethod)
  }
  return optionList.value
})

defineExpose({
  refetchOptionData,
})
</script>

<template>
  <el-select
    v-model="innerValue"
    filterable
    clearable
    :multiple="multiple"
    v-bind="$attrs"
    @change="handleChange"
  >
    <el-option
      v-for="option in filteredList"
      :key="option[props.props?.value]"
      :value="option[props.props?.value]"
      :label="option[props.props?.label]"
      :disabled="disableOptionFnCalc ? disableOptionFnCalc(option) : false"
    />
  </el-select>
</template>
