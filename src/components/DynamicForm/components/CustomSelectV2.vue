<script lang="ts" setup>
import type { DictKey } from '~/services/dict'
import { FETCH_DICT } from '~/enums/dict'

const props = withDefaults(defineProps<{
  optionKey?: DictKey
  options?: any[]
  props?: {
    label?: string
    value?: string
    desc?: string // 描述字段
  }
  immediate?: boolean
}>(), {
  immediate: true,
})

const optionList = ref<any[]>([])
const fetchDict = ref(props.optionKey ? FETCH_DICT.get(props.optionKey) : null)

const propsComp = computed(() => props?.props || fetchDict.value?.props)

watchEffect(() => {
  if (props.options)
    optionList.value = props.options
})

async function query(params = {}) {
  if (props.optionKey)
    optionList.value = await dictCache.getField(props.optionKey, params) || []
}

onMounted(async () => {
  if (props.immediate) {
    query()
  }
})

defineExpose({
  query,
})
</script>

<template>
  <el-select-v2
    :options="options || optionList"
    :props="propsComp"
    filterable
    v-bind="$attrs"
  >
    <template #default="{ item }">
      <slot :item="item" />
      <template v-if="!$slots.default">
        <span style="margin-right: 8px">{{ item[propsComp?.label || 'label'] }}</span>
        <span v-if="propsComp?.desc && item[propsComp?.desc]" style="color: var(--el-text-color-secondary); font-size: 13px">
          {{ item[propsComp.desc] }}
        </span>
      </template>
    </template>
  </el-select-v2>
</template>
