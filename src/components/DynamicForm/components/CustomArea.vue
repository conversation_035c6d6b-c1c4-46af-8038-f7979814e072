<script setup>
import { defineEmits, defineProps, ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  detailBindings: {
    type: Object,
    default: () => ({}),
  },
  showDetail: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'blur', 'change'])
const internalAddressValue = ref(getValue('address'))
const internalDetailValue = ref(getValue('detail'))
const raw = ref({})
const cascaderProps = reactive({
  label: 'regionName',
  value: 'regionId', // 统一传regionId
  lazy: true,
  async lazyLoad(node, resolve) {
    const { level } = node
    try {
      const { data } = await getAreas({ regionParentId: level === 0 ? -1 : node.data?.regionId })
      raw.value[level] = data
      const result = data.map((item) => {
        return { ...item, leaf: level >= 2 }
      })
      resolve(result)
    }
    catch (error) {
      console.error(error)
    }
  },
})

// TODO: api
function getAreas() {}

// 监听地址变化
function handleChangeAddress(newValue) {
  const result = newValue || []
  let resultValue = props.modelValue

  if (result.length === 0)
    return emit('update:modelValue', { province: '', city: '', district: '', detail: internalDetailValue.value })

  resultValue = { ...props.modelValue, province: result[0], city: result[1] || '', district: result[2] || '' }
  const rawData = {}
  for (const i in result) {
    rawData[i] = raw.value[i].find(item => item.regionId === result[i])
  }
  emit('update:modelValue', { ...resultValue, detail: internalDetailValue.value, rawData })
}
// 监听详细地址变化
function handleChangeDetail(newValue) {
  emit('update:modelValue', { ...props.modelValue, detail: newValue })
}

watch(() => props.modelValue, () => {
  internalAddressValue.value = getValue('address')
  internalDetailValue.value = getValue('detail')
}, { deep: true })

// 获取省市区、详细地址的值
function getValue(key) {
  const modelValue = props.modelValue
  if (!modelValue)
    return ''
  if (key === 'address')
    return getAddressValue()
  if (key === 'detail')
    return getDetailValue()

  // 获取地区value
  function getAddressValue() {
    if (!modelValue || modelValue.length === 0)
      return []
    return [modelValue.province, modelValue.city, modelValue.district]
  }
  // 获取详细地址
  function getDetailValue() {
    if (!modelValue)
      return ''
    return modelValue.detail
  }
}
</script>

<template>
  <div class="custom-area">
    <el-cascader
      v-model="internalAddressValue"
      :props="cascaderProps"
      clearable
      @change="handleChangeAddress"
    />
    <el-input
      v-if="showDetail"
      v-model="internalDetailValue"
      v-bind="detailBindings"
      placeholder="请输入详细地址"
      class="custom-area__detail"
      @change="handleChangeDetail"
    />
  </div>
</template>

<style lang="scss" scoped>
.custom-area {
  width: 100%;
  :deep(.el-cascader) {
    width: 100%;
  }
  &__detail {
    vertical-align: top;
    margin-top: 18px;
  }
}
</style>
