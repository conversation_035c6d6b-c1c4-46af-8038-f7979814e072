<script setup>
import debounce from 'lodash.debounce'
import { optionProps } from '~/components/DynamicForm/utils/optionProps'
import { fetchMaps } from '../enum/fetchMap'

const props = defineProps({
  modelValue: [String, Number, Array],
  ...optionProps,
  readonly: {
    type: Boolean,
    default: false,
  },
  queryLabel: {
    type: String,
  },
  apiParams: {
    type: Object,
    default: null,
  },
  group: {
    type: Boolean,
    default: false,
  },
  initLabel: {
    type: Boolean,
    default: false,
  },
  immediate: {
    type: Boolean,
    default: false,
  },
  autoInitLabel: {
    type: Boolean,
    default: true,
  },
  debounceTime: {
    type: Number,
    default: 300,
  },
})
const emit = defineEmits(['update:modelValue', 'change'])

const fetchDetail = fetchMaps.get(props.optionKey)

const innerValue = defineModel({ required: true })

const optionsList = ref([])
const loading = ref(false)

async function remoteMethod(query) {
  if (!query)
    return
  optionsList.value = await fetchList(query)
}

const debounceRemoteMethod = debounce(remoteMethod, props.debounceTime)

const valueKeyCalc = ref((fetchDetail && !Array.isArray(fetchDetail)) ? fetchDetail.value || props.valueKey : 'label')
const labelKeyCalc = ref((fetchDetail && !Array.isArray(fetchDetail)) ? fetchDetail.label || props.labelKey : 'value')

const disableOptionFnCalc = computed(() => {
  return props.disableOptionMethod || fetchDetail.disableOptionFn || null
})

async function fetchList(queryStr, useIdSearch = false) {
  loading.value = true
  if (!fetchDetail) {
    return []
  }

  // 如果fetchDetail是一个数组，则对每个子项进行处理
  if (Array.isArray(fetchDetail)) {
    const combinedResults = []

    for (const detail of fetchDetail) {
      // 对当前子项调用fetchList逻辑，并收集结果
      const result = await processFetchDetail(detail, queryStr, useIdSearch, props)
      combinedResults.push({
        groupName: detail.groupName,
        options: result,
      }) // 假设每次结果是数组，将它们合并
    }
    loading.value = false
    return combinedResults
  }
  else {
    loading.value = false
    // 处理单个fetchDetail对象的情况
    return await processFetchDetail(fetchDetail, queryStr, useIdSearch, props)
  }
}

async function processFetchDetail(fetchDetail, queryStr, useIdSearch, props) {
  const { fn, query = {}, queryLabel: queryLabelFromConfig } = fetchDetail

  const _params = props.apiParams || {}
  const _queryLabel = useIdSearch ? (fetchDetail.queryValue || fetchDetail.value) : (props.queryLabel || queryLabelFromConfig)
  const paramsResult = Object.assign({}, query, _params, { [_queryLabel]: queryStr })

  try {
    const result = await fn(paramsResult)
    return fetchDetail.resultFilter ? fetchDetail.resultFilter(result) : result
  }
  catch (error) {
    console.error(error)
    return []
  }
}

function handleChange(value) {
  let option
  if (props.group) {
    const group = optionsList.value.find(group => group.options.find(item => item.value === value))
    if (group) {
      option = group.options.find(item => item.value === value)
    }
  }
  else {
    option = optionsList.value.find(option => option[valueKeyCalc.value] === value)
  }
  emit('change', value, { option })
}

async function initLabelFn(newValue) {
  if (!newValue)
    return
  const labelOption = optionsList.value.find(option => option[valueKeyCalc.value] === newValue)
  if (newValue && !labelOption)
    optionsList.value = await fetchList(newValue, true)
  // TODO: 处理多选情况
}

watch(() => props.modelValue, async (newValue, oldValue) => {
  if (props.autoInitLabel)
    initLabelFn(newValue, oldValue)
}, {
  immediate: true,
})

async function refetchOptionData() {
  optionsList.value = await fetchList()
}

onMounted(async () => {
  if (props.immediate) {
    refetchOptionData()
  }
})

defineExpose({
  refetchOptionData,
})
</script>

<template>
  <el-select
    v-model="innerValue"
    v-bind="$attrs"
    placeholder="输入名称搜索"
    :remote-method="debounceRemoteMethod"
    :loading="loading"
    filterable clearable remote reserve-keyword
    @change="handleChange"
  >
    <template v-if="!group">
      <el-option
        v-for="item in optionsList"
        :key="item[valueKeyCalc]"
        :label="item[labelKeyCalc]"
        :value="item[valueKeyCalc]"
        :disabled="disableOptionFnCalc ? disableOptionFnCalc(item) : false"
      />
    </template>

    <template v-if="group">
      <el-option-group
        v-for="group in optionsList"
        :key="group.groupName"
        :label="group.groupName"
      >
        <el-option
          v-for="item in group.options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="disableOptionFnCalc ? disableOptionFnCalc(item) : false"
        />
      </el-option-group>
    </template>
  </el-select>
</template>
