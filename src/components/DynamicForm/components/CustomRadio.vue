<script setup>
import { optionProps } from '~/components/DynamicForm/utils/optionProps'
import { useOptions } from '../hooks/useOption'

const props = defineProps({
  modelValue: [String, Number],
  ...optionProps,
})

const { optionList } = useOptions(props)

const internalValue = defineModel({ required: true })
</script>

<template>
  <el-radio-group v-model="internalValue">
    <el-radio
      v-for="option in optionList"
      :key="option[valueKey] || option.value"
      :label="option[labelKey] || option.label"
      :value="option[valueKey] || option.value"
    />
  </el-radio-group>
</template>
