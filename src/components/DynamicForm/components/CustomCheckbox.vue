<script setup>
import { optionProps } from '~/components/DynamicForm/utils/optionProps'
import { useOptions } from '../hooks/useOption'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  ...optionProps,
})

const emit = defineEmits(['update:modelValue'])

const { optionList, labelKeyCalc, valueKeyCalc } = useOptions(props)

const internalValue = ref(props.modelValue)

watch(internalValue, (newValue) => {
  emit('update:modelValue', newValue)
})

watch(() => props.modelValue, (newValue) => {
  internalValue.value = newValue
}, { deep: true })
</script>

<template>
  <el-checkbox-group v-model="internalValue">
    <el-checkbox
      v-for="option in optionList"
      :key="option[valueKeyCalc]"
      :label="option[valueKeyCalc]"
    >
      {{ option[labelKeyCalc] }}
    </el-checkbox>
  </el-checkbox-group>
</template>
