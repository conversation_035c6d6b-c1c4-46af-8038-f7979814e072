<script setup>
import { computed, defineEmits, defineProps } from 'vue'

const props = defineProps({
  modelValue: [String, Number],
  min: {
    type: Number,
    default: 0,
  },
  max: {
    type: Number,
  },
  readonly: <PERSON><PERSON><PERSON>,
})

const emit = defineEmits(['update:modelValue'])

const innerValue = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})
</script>

<template>
  <div v-if="readonly">
    {{ innerValue }}
  </div>
  <el-input-number v-model="innerValue" :min="min" :max="max" :controls="false" v-bind="$attrs" />
</template>
