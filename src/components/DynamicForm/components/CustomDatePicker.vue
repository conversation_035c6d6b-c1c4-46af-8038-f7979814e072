<script setup>
import { computed, defineEmits, defineProps } from 'vue'

const props = defineProps({
  modelValue: [String, Number, Array],
  format: {
    type: String,
  },
  type: {
    type: String,
    default: 'datetime',
  },
  readonly: Boolean,
})
const emit = defineEmits(['update:modelValue'])

const formatConfig = {
  date: 'YYYY-MM-DD',
  month: 'YYYY-MM',
  year: 'YYYY',
  default: 'YYYY-MM-DD HH:mm:ss',
  time: 'HH:mm:ss',
  datetime: 'YYYY-MM-DD HH:mm:ss',
}

const formatCalc = computed(() => {
  return props.format || formatConfig[props.type]
    || formatConfig.default
})

const innerValue = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  },
})
</script>

<template>
  <div v-if="readonly">
    {{ innerValue }}
  </div>
  <el-date-picker
    v-else
    v-model="innerValue"
    :type="type"
    v-bind="$attrs"
    class="custom-date-picker"
    :value-format="formatCalc"
    :format="formatCalc"
  />
</template>

<style lang="scss">
.custom-date-picker {
  flex: 1;
}
</style>
