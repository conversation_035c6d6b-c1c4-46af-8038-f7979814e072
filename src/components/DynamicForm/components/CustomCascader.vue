<script setup>
import { optionProps } from '~/components/DynamicForm/utils/optionProps'

const props = defineProps({
  modelValue: [String, Number, Array],
  ...optionProps,
  props: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue'])
const { optionList, labelValue } = useOptions(props)

const cascaderProps = computed(() => {
  return {
    label: labelValue.value?.label || 'label',
    value: labelValue.value?.value || 'id',
  }
})

const innerValue = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})
</script>

<template>
  <ElCascader
    v-model="innerValue"
    :options="optionList"
    :props="cascaderProps"
    filterable
    clearable
  />
</template>
