// useDynamicForm.js
import { computed, nextTick, ref } from 'vue'
import componentMap from '~/components/DynamicForm/components/index'
import { typeToComponentNameMap } from '~/components/DynamicForm/DynamicForm.config'
import { setDefaultRulesFromFieldType } from '~/components/DynamicForm/DynamicForm.rule'
import { placeholderPrefix } from '~/components/DynamicForm/DynamicForm.utils'

/**
 * useDynamicForm
 * @param props
 * @param emit
 * @returns
 */
export function useDynamicForm(props, emit) {
  const formRef = ref(null)

  const formFields = computed(() =>
    Object.keys(props.fields).map((key) => {
      const isMultipleKey = key.includes('-')
      const _keysBindings = {}
      const _keys = key.split('-')

      if (isMultipleKey) {
        _keys.forEach((k) => {
          _keysBindings[`${k}`] = props.modelValue[k]
          // _keysBindings[`onUpdate:${k}`] = (e) => updateFieldValue(k, e) // TODO: 多个属性同时更新时会有问题，暂时不用这个，使用了update:modelValue切分值
        })
      }
      return ({
        ...props.fields[key],
        model: !isMultipleKey ? key : _keys[0], // TODO: !isMultipleKey ? key : _keys[0] 配合上面那个todo，暂时不这样做 规则：modelValue绑定在第一个值上
        allModel: key, // 完整的model key，如'a-b'形式的多个model拼接
        bindings: {
          placeholder: placeholderPrefix(props.fields[key]) + (props.fields[key].label || ''),
          ...props.fields[key].bindings,
          ..._keysBindings,
        },
        rules: setDefaultRulesFromFieldType(props.fields[key], props.modelValue[key]),
      })
    }).sort((a, b) => a.order - b.order).filter(i => !i.hidden),
  )

  const rowComponent = computed(() => props.useGrid ? 'el-row' : 'div')
  const colComponent = computed(() => props.useGrid ? 'el-col' : 'div')

  const updateFieldValue = (field, value) => {
    const modelValueCopy = { ...props.modelValue }

    if (field.includes('-')) {
      // 多重键处理
      const keys = field.split('-')
      Object.assign(modelValueCopy, Object.fromEntries(keys.map(k => [k, value[k]])))
    }
    else {
      // 单一键处理
      modelValueCopy[field] = value
    }

    // 发出更新模型值的事件
    emit('update:modelValue', modelValueCopy)
    // 发出表单变更的事件
    emit('formChange', { field, value })
  }

  const resolveComponent = (type) => {
    const componentName = typeToComponentNameMap[type] || null
    return componentMap.get(componentName) || null
  }

  const getDisplayLabel = (field) => {
    if (!field.displayLabel)
      return props.modelValue[field.model]
    if (typeof field.displayLabel === 'function')
      return field.displayLabel({ field, formData: props.modelValue })
    return props.modelValue[field.displayLabel]
  }

  // 表单校验
  const validate = () => {
    return new Promise((resolve) => {
      formRef.value.validate((valid) => {
        resolve(valid)
      })
    })
  }
  const validateField = (key) => {
    nextTick(() => {
      formRef.value.validateField(key)
    })
  }

  const clearValidate = () => {
    nextTick(() => {
      formRef.value.clearValidate()
    })
  }

  const submit = () => {
    validate().then((valid) => {
      if (valid)
        emit('submit')
    })
  }

  return { formRef, formFields, updateFieldValue, resolveComponent, validate, rowComponent, colComponent, validateField, submit, clearValidate, getDisplayLabel }
}
