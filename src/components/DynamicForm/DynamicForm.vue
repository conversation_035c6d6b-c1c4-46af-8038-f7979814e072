<script lang="ts" setup>
import type { Field } from './types/form'
import { useOutLabelField } from './DynamicForm.config'
import { useDynamicForm } from './useDynamicForm'

interface Props {
  type?: string //
  modelValue: Record<string, any> // 表单数据模型
  fields: Record<string, Field> // 表单字段配置
  colSpan?: number // 栅格占据的列数
  rowGutter?: number // 栅格间隔
  labelWidth?: string // 标签宽度
  labelPosition?: 'left' | 'right' | 'top' // 表单标签的位置
  useGrid?: boolean // 是否使用栅格布局
  excludeFoldNum?: number // 折叠字段数量，0 表示不折叠
}

const props = withDefaults(defineProps<Props>(), {
  colSpan: 8,
  rowGutter: 10,
  labelPosition: 'right',
  useGrid: true,
  excludeFoldNum: 0,
})

const emit = defineEmits(['update:modelValue', 'formChange', 'submit', 'reset'])
const formItemRefs = ref(new Map())
function componentRefs(el, model) {
  if (el)
    formItemRefs.value.set(model, el)
}
const { formRef, formFields, updateFieldValue, resolveComponent, validate, rowComponent, colComponent, validateField, clearValidate, getDisplayLabel } = useDynamicForm(props, emit)

// 导出 validate 函数
defineExpose({ validate, validateField, clearValidate, formItemRefs })
</script>

<template>
  <el-form
    ref="formRef"
    class="custom-form"
    :model="modelValue"
    :label-width="labelWidth"
    :label-position="labelPosition"
  >
    <component
      :is="rowComponent"
      :class="{ 'form-row': !useGrid }"
      :gutter="rowGutter"
    >
      <template
        v-for="(field, index) in formFields"
        :key="field.model"
      >
        <component
          :is="colComponent"
          v-show="!field.hide && ((props.excludeFoldNum && index < props.excludeFoldNum) || !props.excludeFoldNum)"
          :class="{ 'form-col': !useGrid }"
          :span="field.colSpan || colSpan"
        >
          <el-form-item
            :label="field.label"
            :rules="field.rules"
            :prop="field.model"
            v-bind="field.formItemBindings"
          >
            <template
              v-if="field.icon"
              #label
            >
              <i
                class="form-item__label-icon"
                :class="field.icon"
              />
              {{ field.label }}
            </template>
            <slot
              v-if="field.type === 'slot'"
              :name="field.model"
              :field="field"
              :value="modelValue[field.model]"
              :update-field-value="(value) => updateFieldValue(field.model, value)"
            />
            <template v-else-if="useOutLabelField.includes(field.type) && type === 'readonly'">
              {{ getDisplayLabel(field) || '' }}
            </template>
            <component
              :is="resolveComponent(field.type)"
              v-else
              :ref="(el) => componentRefs(el, field.model)"
              :model-value="modelValue[field.model]"
              :readonly="props.type === 'readonly'"
              v-bind="field.bindings"
              @update:model-value="updateFieldValue(field.allModel, $event)"
            />
          </el-form-item>
        </component>
      </template>
    </component>
  </el-form>
</template>
