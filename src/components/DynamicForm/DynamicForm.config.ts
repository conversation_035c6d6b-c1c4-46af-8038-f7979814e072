export const typeToComponentNameMap = {
  input: 'CustomInput',
  select: 'CustomSelect',
  checkbox: 'CustomCheckbox',
  radio: 'CustomRadio',
  area: 'CustomArea',
  show: 'CustomShow',
  cascader: 'CustomCascader',
  treeSelect: 'CustomTreeSelect',
  inputNumber: 'CustomInputNumber',
  date: 'CustomDate',
  asyncSelect: 'CustomAsyncSelect',
  switch: 'CustomSwitch',
  select2: 'CustomSelectV2',
}

export const requiredTextMap = {
  input: '请输入',
  checkbox: '请选择',
  radio: '请选择',
  area: '请选择',
  select: '请选择',
  treeSelect: '请选择',
  cascader: '请选择',
  asyncSelect: '请输入名称搜索',
  switchDate: '请选择',
}

// 使用外部只读控件
export const useOutLabelField = ['asyncSelect', 'inputNumber']
