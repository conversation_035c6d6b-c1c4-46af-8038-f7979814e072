<script lang="ts" setup>
import { ClickOutside as vClickOutside } from 'element-plus'
import { useGlobalPopover } from '~/composables/useGlobalPopover'

const { component, virtualRef, popoverRef, visible, closePopover, state } = useGlobalPopover()
</script>

<template>
  <el-popover
    ref="popoverRef"
    :virtual-ref="virtualRef"
    :visible="visible"
    virtual-triggering
    width="auto"
    popper-class="global-popover"
  >
    <div v-click-outside="closePopover" class="global-popover__content">
      <component :is="component" v-bind="state?.bindings" />
    </div>
  </el-popover>
</template>

<style lang="scss">
.global-popover {
  --el-popover-padding: 0;
  &__content {
    @apply p-3;
  }
}
</style>
