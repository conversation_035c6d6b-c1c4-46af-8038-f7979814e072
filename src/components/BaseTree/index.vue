<script lang="ts" setup>
defineProps<{
  fileIcon?: string
  folderIcon?: string
}>()
const { visible } = useGlobalPopover()
const treeRef = ref()
const activeNode = ref()

// 用于记录操作节点，点击子操作节点弹窗popover时，显示操作按钮
function handleClick(node) {
  activeNode.value = node
}

defineExpose({
  treeRef,
})
</script>

<template>
  <el-tree
    ref="treeRef"
    v-bind="$attrs"
    style="max-width: 600px"
    class="base-tree"
  >
    <template #default="{ node, data }">
      <div class="group flex flex-1 items-center gap-1 overflow-hidden">
        <i v-if="folderIcon && data.nodeType === '1'" class="flex-[0_0_18px]" :class="folderIcon" />
        <i v-if="data.nodeType !== '1' && fileIcon" class="flex-[0_0_18px]" :class="fileIcon" />
        <span class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap" :title="node.label">{{ node.label }}</span>
        <div v-if="$slots.actions" class="hidden gap-1 fs-18 group-hover:flex" :class="{ 'flex!': visible && activeNode?.id === node.id }" @click.stop @click="handleClick(node)">
          <slot name="actions" :node="node" :data="data" />
        </div>
      </div>
    </template>
  </el-tree>
</template>

<style lang="scss" scoped>
.base-tree {
  --el-tree-node-content-height: 28px;
  :deep(.el-tree-node__content) {
    @apply border-rd-1;
  }
}
</style>
