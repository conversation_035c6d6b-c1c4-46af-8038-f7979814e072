<script lang="ts" setup>
import { sql } from '@codemirror/lang-sql'
import { format } from 'sql-formatter'
import { smoothy } from 'thememirror'
import { Codemirror } from 'vue-codemirror'

const props = withDefaults(defineProps<{
  modelValue: string
  showFormat?: boolean
  lang?: 'sql' | 'log'
}>(), {
  lang: 'sql',
})
const code = defineModel<string>({ required: true })
const extensions = [
  sql(),
  smoothy,
]
const options = {
  mode: props.lang,
}
defineExpose({
  format: () => code.value = format(code.value, { language: 'sql' }),
})
</script>

<template>
  <div class="relative flex">
    <span v-if="showFormat" class="absolute right-2 top-1 z-2 cursor-pointer rounded-md p-1 line-height-0 hover:bg-slate-200">
      <i class="i-ph-paint-brush-household text-base color-slate-500" @click="format" />
    </span>
    <Codemirror v-model="code" :tab-size="2" indent-with-tab :extensions="extensions" :options="options" v-bind="$attrs" />
  </div>
</template>
