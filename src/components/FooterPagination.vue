<script lang="ts" setup>
import type { ComponentSize } from 'element-plus'

withDefaults(defineProps<{
  isFullWidth?: boolean // 是否全宽模式，默认为false
  total?: number // 总条目数
}>(), {
  total: 0,
})
const emit = defineEmits(['change'])

const page = defineModel<number>('page', { default: 1 })
const pageSize = defineModel<number>('pageSize', { default: 10 })
const size = ref<ComponentSize>('default')

function handleSizeChange(size: number) {
  emit('change', {
    pageSize: size,
    pageNum: page.value,
  })
}
function handleCurrentChange(num: number) {
  emit('change', {
    pageSize: pageSize.value,
    pageNum: num,
  })
}
</script>

<template>
  <el-footer class="footer-pagination-wrap w-full flex items-center justify-end bg-white" :class="{ 'is-full-width': isFullWidth }">
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="pageSize"
      class="custom-pagination"
      :size="size"
      background
      layout="total, prev, pager, next, sizes"
      :total="total"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </el-footer>
</template>

<style lang="scss" scoped>
.footer-pagination-wrap.is-full-width {
  @apply relative;
  &::before {
    content: '';
    display: flex;
    width: calc(100% + 40px);
    position: absolute;
    z-index: 0;
    left: -20px;
    @apply h-full bg-white;
  }
}
:deep(.custom-pagination.el-pagination) {
  --el-pagination-button-bg-color: rgba(0, 0, 0, 0);
  --pagination-button-border-color: #dcdee0;
  --el-pagination-border-radius: 4px;
  @apply relative z-1;
  .el-pager li {
    border: 1px solid var(--pagination-button-border-color);
    &.is-active {
      font-weight: 500;
      background-color: unset;
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }
  }
  .btn-prev:disabled,
  .btn-next:disabled {
    background-color: unset;
  }
}
</style>
