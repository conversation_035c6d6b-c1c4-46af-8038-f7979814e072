<script lang="ts" setup>
import type { TableInstance } from 'element-plus'
import type { LocalDictKey } from '~/enums/dict.js'
import type { DictKey } from '~/services/dict.js'
import { componentMap, typeToComponentNameMap } from '~/components/DynamicForm'
import { DICTS } from '~/enums/dict.js'
import { popUp } from '~/utils/popup.js'
import { formatterMaps } from './utils/formatter.js'

interface Column {
  label: string // 标题
  prop: string // 字段值
  type?: string // 单元格类型，可选值为：
  formatter?: (row: any, column: any) => any // 单元格格式化函数，传入行数据和列定义对象
  slotName?: string // 插槽名称
  optionKey?: DictKey // 字典key
}
interface Action {
  name: string // 名称
  color?: string
  handler?: ({ row, index }: { row: any, index: number }) => void
  props?: (scope: { row: any }) => Record<string, any>
}
export interface TableProps {
  columns: Column[]
  loading?: boolean // 是否显示加载中
  showIndex?: boolean // 是否显示序号列
  showSelect?: boolean // 是否显示选择框
  selectable?: (row: any) => boolean
  actions?: Action[]
  showPage?: boolean // 是否显示分页
  total?: number
  actionWidth?: number | string
}

// Props
const props = withDefaults(defineProps<TableProps>(), {
  showIndex: false,
  showSelect: false,
  loading: false,
  total: 0,
  showPage: true,
})

const emit = defineEmits<{
  (e: 'action', data: { action: Action, row: any, index: number }): void
  (e: 'pageChange', data: { page: number, pageSize: number }): void
  (e: 'change', data: { row: any, $index: number }): void
}>()

const tableRef = ref<TableInstance>()
// Pagination state
const page = ref(1)
const pageSize = ref(10)
const rect = ref()

function pageReset({ noEmit } = { noEmit: false }) {
  page.value = 1
  if (noEmit)
    return
  emit('pageChange', { page: page.value, pageSize: pageSize.value })
}

function showValue(column: Column, scope) {
  // 字典字段处理
  if (column.optionKey) {
    const list = DICTS[column.optionKey as LocalDictKey]
    const findLabel = list?.find(item => item.value === scope.row[column.prop])
    return findLabel?.label || scope.row[column.prop]
  }

  return formatterComp(column) ? formatterComp(column)({ row: scope.row, cellValue: scope.row[column.prop] }) : scope.row[column.prop]
}

function handleChangePagination(params: { pageNum: number, pageSize: number }) {
  page.value = params.pageNum
  pageSize.value = params.pageSize
  emit('pageChange', { page: page.value, pageSize: pageSize.value })
}

function formatterComp(column: Column) {
  if (typeof column.formatter === 'string')
    return cellValue => formatterMaps[column.formatter](cellValue)

  else if (typeof column.formatter === 'function')
    return column.formatter

  else return undefined
}

function widthValue(width: number | undefined | string) {
  return (width as number && +(width as string)) > 0 ? width : props.actions ? (46 * props.actions.length + 24) : ''
}

async function handleAction(action, row, index) {
  // 如果操作是"删除"，首先显示确认弹出窗口
  if (action.name === '删除') {
    if (action.beforeHandler) {
      const status = await action.beforeHandler({ row, index })
      // 如果用户取消操作，则直接返回不执行后续动作
      if (!status) {
        return
      }
    }
    const status = await popUp('确认删除吗？')
    // 如果用户取消操作，则直接返回不执行后续动作
    if (!status) {
      return
    }
  }

  action.handler({ row, index })
  emit('action', { action, row, index })
}

function toggleSelection(rows) {
  if (rows) {
    rows.forEach((row) => {
      tableRef.value!.toggleRowSelection(row, undefined)
    })
  }
  else {
    tableRef.value!.clearSelection()
  }
}

function getRowKeys(row) {
  return row[props.rowKey]
}

function handleChangeFormItem(scope) {
  emit('change', 'form-item', scope)
}

onMounted(() => {
  nextTick(() => {
    if (tableRef.value)
      rect.value = tableRef.value.$el.getBoundingClientRect()
  })
})

defineExpose({
  tableRef,
  rect,
  toggleSelection,
  pageReset,
})
</script>

<template>
  <div class="h-full flex flex-col">
    <ElTable
      ref="tableRef"
      v-loading="loading"
      :class="props.class"
      :row-key="getRowKeys"
      class="flex-1"
      v-bind="$attrs"
    >
      <slot name="column" />
      <template #empty>
        <BaseEmpty />
      </template>
      <ElTableColumn
        v-if="showSelect"
        :selectable="selectable"
        reserve-selection
        type="selection"
        width="55"
        align="center"
      />
      <ElTableColumn
        v-if="showIndex"
        type="index"
        label="序号"
        width="70"
        align="center"
      />
      <ElTableColumn
        v-for="column in columns"
        :key="column.prop"
        align="left"
        v-bind="column"
        show-overflow-tooltip
      >
        <template #default="scope">
          <template v-if="column.slotName">
            <slot
              :name="column.slotName"
              :column="column"
              :row="scope.row"
              :prop="column.prop"
              :index="scope.$index"
            />
          </template>
          <template v-else-if="!column.type">
            <!-- 默认渲染数据列 -->
            {{ showValue(column, scope) }}
          </template>
          <template v-else-if="column.type === 'tag'">
            <ElTag
              :type="column.tagColors ? column.tagColors[scope.row[column.prop]] : ''"
            >
              {{ showValue(column, scope) }}
            </ElTag>
          </template>
          <template v-else>
            <component :is="componentMap.get(typeToComponentNameMap[column.type])" v-model="scope.row[column.prop]" v-bind="column.bindings" @change="handleChangeFormItem(scope)" />
          </template>
        </template>
      </ElTableColumn>
      <ElTableColumn
        v-if="actions && actions.length > 0"
        fixed="right"
        label="操作"
        align="center"
        :min-width="widthValue(actionWidth)"
        :width="actionWidth || 'auto'"
      >
        <template #default="scope">
          <template v-for="(action, index) in actions" :key="action.name || index">
            <el-button
              type="primary"
              :style="action.color ? `--el-button-text-color: ${action.color}` : ''"
              link
              v-bind="action.props?.(scope)"
              @click="handleAction(action, scope.row, scope.$index)"
            >
              {{ action.name }}
            </el-button>
          </template>
        </template>
      </ElTableColumn>
    </ElTable>
    <FooterPagination
      v-if="showPage" v-model:page="page"
      v-model:page-size="pageSize"
      :total="total"
      @change="handleChangePagination"
    />
  </div>
</template>

<style lang="scss" scoped>
.table-page {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}
.theme-green .table-page {
  justify-content: flex-end;
  padding: 30px 0;
}
</style>
