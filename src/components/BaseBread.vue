<script lang="ts" setup>
import type { MenuItem } from '~/enums/menu'
import { MENUS } from '~/enums/menu'
import { findRouteByPath } from '~/utils/menu'

const route = useRoute()
const breadsStore = useBreadsStore()

// 面包屑数据
const breads = computed(() => breadsStore.breads)

watch(() => route.path, () => {
  breadsStore.setBreads(formatBreads(findRouteByPath(MENUS, route)))
})

onMounted(() => {
  init()
})

// 处理查找到的面包屑数据，多级菜单时，剔除顶级菜单
function formatBreads(menus: MenuItem[] | null) {
  if (!menus)
    return []

  // 顶级菜单只有一个
  if (menus?.length <= 1)
    return menus
  else return menus?.slice(1) || []
}

function init() {
  // 最新的面包屑
  const breadsLatest = formatBreads(findRouteByPath(MENUS, route))
  // 对比最新的面包屑和存储的面包屑，不一致时重新设置面包屑(存储的面包屑中包含当前路由，最新的面包屑中可能不包含历史父级当前路由)
  const latestResult = breadsLatest.map(i => i.path).join('|')
  const storageResult = breadsStore.breads.map(i => i.path).join('|')
  if (latestResult !== storageResult)
    breadsStore.setBreads(formatBreads(findRouteByPath(MENUS, route)))
}
</script>

<template>
  <el-breadcrumb class="bread-title" separator="/">
    <el-breadcrumb-item v-for="item in breads" :key="item.path" :to="item.currentRoute || item.path">
      {{ item.title }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<style lang="scss">
.bread-title {
  @apply py-1.5;
}
</style>
