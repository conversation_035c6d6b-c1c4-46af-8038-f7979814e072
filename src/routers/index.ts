import NProgress from 'nprogress'
import { setupLayouts } from 'virtual:generated-layouts'
import { createRouter, createWebHashHistory } from 'vue-router'
import { routes } from 'vue-router/auto-routes'
import { authService } from '~/services/auth'
import { useUserStore } from '~/stores/user'
import { getToken } from '~/utils/token'

NProgress.configure({
  showSpinner: false,
})

export const router = createRouter({
  routes: setupLayouts(routes),
  history: createWebHashHistory(import.meta.env.BASE_URL),
})

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (['/', '/login'].includes(to.path)) {
    next()
  }

  if (!getToken()) {
    next(`/login?redirect=${to.fullPath}`)
  }
  else {
    if (useUserStore().user) {
      next()
    }
    else {
      authService.getUser().then((user) => {
        useUserStore().setUser(user)
        next()
      })
    }
  }
})
router.afterEach(() => {
  NProgress.done()
})
