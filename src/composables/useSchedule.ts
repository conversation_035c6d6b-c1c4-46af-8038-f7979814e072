import { DICTS } from '~/enums/dict'
import { ruleScheduleServices } from '~/services/rule'

// 调度
export function useSchedule() {
  // 获取调度列表
  async function queryScheduleList(rulesetId: number) {
    try {
      const result = await ruleScheduleServices.list({ rulesetId })
      return result.map((item) => {
        const scheduleTypeName = DICTS.scheduleType.find(one => one.value === item.scheduleType)?.label
        return {
          ...item,
          scheduleTypeName,
          label: `${item.scheduleName}(${scheduleTypeName})`,
        }
      })
    }
    catch (e) {
      console.error(e)
    }
  }
  return {
    queryScheduleList,
  }
}
