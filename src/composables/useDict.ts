import type { Reactive } from 'vue'
import type { LocalDictKey } from '~/enums/dict'
import type { DictKey } from '~/services/dict'
import { reactive } from 'vue'
import { DICTS, FETCH_DICT } from '~/enums/dict'
import { dictService } from '~/services/dict'

class DictCache {
  cache: Record<DictKey, any[]>
  pendingRequests: Map<any, any>
  fetchFunction: (code: any, params?: Record<string, any>) => Promise<unknown>
  constructor(fetchFunction: (code: any) => Promise<unknown>) {
    this.cache = reactive<Record<DictKey, any[]>>({}) // 响应式缓存
    this.pendingRequests = new Map() // 正在进行中的请求
    this.fetchFunction = fetchFunction // 请求方法
  }

  /**
   * 获取字段数据
   * @param {string} code 字段代码
   * @returns {Promise<*>} 返回字段数据（响应式）
   */
  async getField(code: DictKey, params?: Record<string, any>): Promise<any[] | undefined> {
    if (DICTS[code as LocalDictKey])
      return DICTS[code as LocalDictKey]
    // 如果缓存中已有，直接返回响应式数据
    if (this.cache[code]) {
      return this.cache[code]
    }

    // 如果请求正在进行，返回该请求的 Promise
    if (this.pendingRequests.has(code)) {
      await this.pendingRequests.get(code)
      return this.cache[code]
    }

    // 发起新的请求
    const request = this.fetchFunction(code, params)
      .then((data) => {
        // 处理结果，如果存在结果过滤器，则使用该过滤器处理数据
        const result = FETCH_DICT.has(code) && FETCH_DICT.get(code)?.resultFilter
          ? (FETCH_DICT.get(code)?.resultFilter?.(data) || data)
          : data
        this.cache[code] = result as any[] // 存入缓存（响应式）
      })
      .finally(() => {
        this.pendingRequests.delete(code) // 移除请求标记
      })

    this.pendingRequests.set(code, request)
    await request // 等待请求完成
    return this.cache[code]
  }

  /**
   * 批量获取字段数据
   * @param {string[]} codes 字段代码列表
   * @returns {Promise<object>} 返回包含多个字段的响应式对象
   */
  async getFields(codes) {
    const results = {}
    for (const code of codes) {
      results[code] = await this.getField(code)
    }
    return results
  }

  // 清除某项缓存
  clearCache(code: DictKey) {
    delete this.cache[code]
  }
}

// 定义一个全局的字典缓存实例
async function fetchDictData(code: DictKey, params?: Record<string, any>): Promise<any[]> {
  if (FETCH_DICT.get(code))
    return FETCH_DICT.get(code)!.fn(params)
  return dictService.queryDictByCode(code)
}
export const dictCache = new DictCache(fetchDictData)

/**
 * Vue 组合式函数：useDict
 * @param {string[]} codes 字段代码列表
 * @returns {object} 响应式字段数据
 */
export function useDict<T extends DictKey[]>(codes: T): Reactive<Record<T[number], any[]>> {
  const dicts = reactive<Record<T[number], any[]>>(
    // 使用 reduce 初始化对象
    codes.reduce((pre, cur) => {
      pre[cur as T[number]] = [] // 初始化每个 code 对应的字段为空数组
      return pre
    }, {} as Record<T[number], any[]>),
  )

  // 异步请求数据并更新字典
  codes.forEach((code) => {
    dictCache.getField(code).then((data) => {
      dicts[code] = data as any[] // 更新字典
    })
  })

  return dicts // 返回的 dicts 只包含传入的 codes 键
}
