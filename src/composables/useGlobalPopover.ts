const component = shallowRef()
const virtualRef = ref()
const popoverRef = ref()
const visible = ref(false)
const state = ref()
export function useGlobalPopover() {
  function openPopover(comp, e, params: any) {
    component.value = comp
    virtualRef.value = e.target
    state.value = params
    visible.value = true
  }

  function closePopover() {
    visible.value = false
    component.value = null
    virtualRef.value = null
    state.value = null
  }

  return {
    component,
    virtualRef,
    openPopover,
    popoverRef,
    visible,
    closePopover,
    state,
  }
}
