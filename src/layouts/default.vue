<script lang="ts" setup>
import Logo from '~/assets/images/logo.png'

const route = useRoute()
const { isCollapsed } = useCollapse()

// 是否全屏模式
const isFullScreen = computed(() => route?.meta?.fullScreen === '1')
</script>

<template>
  <el-container class="h-100vh">
    <el-aside v-if="!isFullScreen" class="layout-side" :width="isCollapsed ? '64px' : '220px'">
      <el-header v-show="!isCollapsed" class="flex items-center justify-center pb-5">
        <img :src="Logo" width="85" height="28" class="mr-2" alt="天才系统">
        <h1 class="bold whitespace-nowrap text-xl">
          天才系统
        </h1>
      </el-header>
      <side-menu class="h-[calc(100%-60px)] overflow-y-auto" />
    </el-aside>
    <el-container overflow-hidden>
      <el-header v-if="!isFullScreen" class="layout-header">
        <TopHeader />
      </el-header>
      <el-main class="layout-main bg-#EFF1F5">
        <router-view v-slot="{ Component, route }">
          <transition name="slide" mode="out-in">
            <component :is="Component" :key="route.fullPath" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<style lang="scss" scoped>
.layout-side {
  box-shadow: 0px 10px 20px -4px rgba(56, 83, 176, 0.1);
  transition: width 0.3s;
  @apply bg-white relative z-2;
}
.layout-header {
  box-shadow: 0px 10px 20px -4px rgba(56, 83, 176, 0.1);
  @apply bg-#FDFDFD flex items-center justify-between;
}
.layout-main {
  @apply p-0;
}

/* fade-transform */
.slide--move,
.slide-leave-active,
.slide-enter-active {
  transition: all 0.3s;
}

.slide-enter {
  opacity: 0;
  transform: translateX(-10px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(10px);
}
</style>
