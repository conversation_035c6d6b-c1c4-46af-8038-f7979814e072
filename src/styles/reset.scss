/* 默认情况下隐藏滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  opacity: 0; /* 隐藏滚动条 */
  transition: opacity 0.3s ease; /* 添加平滑过渡 */
}

/* 鼠标悬停时显示滚动条 */
::-webkit-scrollbar:hover {
  opacity: 1;
}

/* 自定义滚动条轨道 */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* 自定义滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

/* 滑块悬停时改变颜色 */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

:focus-visible {
  outline: none;
}
