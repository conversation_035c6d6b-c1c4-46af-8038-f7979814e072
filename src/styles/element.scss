// 指令引用的css手动引入 如，使用ElMessage
// @use 'element-plus/theme-chalk/src/message.scss' as *;
// @use 'element-plus/theme-chalk/src/loading.scss' as *;
// @use 'element-plus/theme-chalk/src/message-box.scss' as *;
// @use 'element-plus/theme-chalk/src/notification.scss' as *;


@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #0575FF,
    ),
  ),
  $table: (
    'header-bg-color': var(--el-fill-color-light),
    'header-text-color': var(--el-text-color-primary),
  )
  // $button-padding-horizontal: (
  //   'default': 80px,
  // )
);

// @use 'element-plus/theme-chalk/src/message.scss' as *;
// @use 'element-plus/theme-chalk/src/loading.scss' as *;
// @use 'element-plus/theme-chalk/src/message-box.scss' as *;
// @use 'element-plus/theme-chalk/src/notification.scss' as *;
