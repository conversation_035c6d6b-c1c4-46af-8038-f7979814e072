@use './reset.scss';
@use './var.scss';
@use './icon.scss';

html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}

html.dark {
  background: #121212;
}

html {
  color: #323333;
}

.form-card {
  @apply bg-white pt-5 px-5 pb-2.5;
}

.custom-form {
  .el-input, .el-select {
    // max-width: 280px;
  }
}

.cm-editor {
  @apply rounded-md overflow-hidden flex-1;
  &.cm-focused {
    outline: 2px solid var(--el-color-primary-light-3);
  }
}

// body .el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell {
//   border-bottom: 1px solid var(--el-table-header-bg-color);
// }

.dot {
  display: inline-block;
  vertical-align: middle;
  background-color: gray;
  @apply w-1.5 h-1.5 rounded-full;
}

