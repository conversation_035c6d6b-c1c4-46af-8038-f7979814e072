import apiClient from '~/utils/request'

// 规则调度服务
export interface RuleScheduleParams {
  rulesetId: number
  scheduleName: string
  cronExpression: string
  scheduleType: string
}

export const scheduleServices = {
  // 分页查询调度任务列表
  list: (params: any) => {
    return apiClient.get('/rule_schedule/page', params)
  },
  // 启动定时任务
  start: (scheduleId: number) => {
    return apiClient.post('/rule_schedule/start', { scheduleId }, { useForm: true })
  },
  // 停止定时任务
  stop: (scheduleId: number) => {
    return apiClient.post('/rule_schedule/stop', { scheduleId }, { useForm: true })
  },
  // 运行定时任务
  run: (scheduleId: number) => {
    return apiClient.post('/rule_schedule/execute', { scheduleId }, { useForm: true })
  },
  // 删除调度
  delete: (scheduleId: number) => {
    return apiClient.post('/rule_schedule/delete', { scheduleId }, { useForm: true })
  },
}
