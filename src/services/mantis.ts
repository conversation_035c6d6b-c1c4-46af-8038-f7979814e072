import apiClient from '~/utils/request'

export interface MantisProject {
  id: number
  name: string
}

export interface MantisUser {
  id: number
  name: string
}

// Mantis 服务
export const mantisService = {
  // 获取项目列表
  getProjects: (): Promise<MantisProject[]> => {
    return apiClient.get('/mantis/projects')
  },

  // 获取用户列表
  getUsers: (): Promise<MantisUser[]> => {
    return apiClient.get('/mantis/users')
  },
}
