import type { ListParams } from '~/types/common'
import apiClient from '~/utils/request'

export interface RuleSetDetail {
  id: number
  name: string // 规则集名称
  projectId: number // 项目ID
  remark: string // 描述
  type: string // 规则集类型
  qualityPersons: any[] // 质量负责人
  createAt: string // 创建时间
  createBy: string // 创建人
  createByName: string // 创建人姓名
  updateBy: string // 更新人
  updateAt: string // 更新时间
  updateByName: string // 更新人姓名
  enabled: boolean // 是否启用
  projectName: string // 项目名称
}
export interface RuleSetParams extends ListParams {
  projectId?: number
  rulesetName?: string
}

export interface RuleSetCreateParams {
  name: string // 规则集名称
  projectId: number // 项目ID
  remark: string // 描述
  qualityPersons: any[] // 质量负责人
  qualityScore: number // 质量评分
}

export interface RuleSetUpdateParams extends RuleSetCreateParams {
  id: number
}

export const ruleSetServices = {
  list: (params: RuleSetParams) => {
    return apiClient.get(`/ruleset/list`, params)
  },
  detail: (id: number) => {
    return apiClient.get(`/ruleset/select/${id}`)
  },
  delete: (id: number) => {
    return apiClient.delete(`/ruleset/delete/${id}`)
  },
  // 新增
  create: (params: RuleSetCreateParams) => {
    return apiClient.post(`/ruleset/save`, params)
  },
  update: (params: RuleSetUpdateParams) => {
    return apiClient.put(`/ruleset/update`, params)
  },
  switch: (params: { rulesetId: number, switchStatus: boolean }) => {
    return apiClient.post(`/ruleset/check-switch/update`, params, { useForm: true })
  },
  run: (params: { rulesetId: number, scheduleId: number, bizDate: string }) => {
    return apiClient.post(`/ruleset/run`, params, { useForm: true })
  },
}
