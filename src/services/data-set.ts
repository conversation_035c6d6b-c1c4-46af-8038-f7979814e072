import apiClient from '~/utils/request'

export interface DataSetDetail {
  name: string
  id: number
  description: string | null
  projectName: string
  dataSourceName: string
  dataSetMetaList?: {
    columnName: string
    columnAlias: string
  }[]
  sqlText: string | null
}

export const dataSetService = {
  list: (params: any) => {
    return apiClient.get('/data_set/list', params)
  },
  selectList: (params: any) => {
    return apiClient.get('/data_set/search', params)
  },
  create: (params: any) => {
    return apiClient.post('/data_set/save', params)
  },
  update: (params: any) => {
    return apiClient.put('/data_set/update', params)
  },
  delete: (id: number) => {
    return apiClient.delete(`/data_set/delete/${id}`)
  },
  // 根据ID查询数据集详情
  detail: (id: number) => {
    return apiClient.get(`/data_set/select/${id}`) as Promise<DataSetDetail>
  },
  // 运行
  run: (params: { dataSetId: number, sql: string }) => {
    return apiClient.post('/data_set/preview', params, { useForm: true })
  },
  // 保存aql
  saveSql: (params: { dataSetId: number, sql: string }) => {
    return apiClient.post('/data_set/sql/save', params, { useForm: true })
  },
  // 根据数据集ID查询元信息 TODO: 重复接口
  meta: (params = {}) => {
    return apiClient.get(`/data_set/select`, { meta: '', ...(params || {}) })
  },
  // 树（包含数据集
  tree: (params?: any) => {
    return apiClient.get('/dataset-groups/tree/select', { dataSet: '', ...(params || {}) })
  },
  // 树（仅包含目录
  treeCatalog: (params?: any) => {
    return apiClient.get('/dataset-groups/tree/select', params)
  },
  // 创建目录
  createGroup: (params: any) => {
    return apiClient.post('/dataset-groups/save', params)
  },
  // 删除目录
  deleteGroup: (id: number) => {
    return apiClient.delete(`/dataset-groups/delete/${id}`)
  },
  // 移动数据集到分组
  moveDataSetToGroup: (params: { dataSetId: string, targetGroupId: string }) => {
    return apiClient.post('/data_set/move', params, { useForm: true })
  },
  // 移动到指定分组
  moveGroupToGroup: (params: { groupId: string, targetGroupId: string }) => {
    return apiClient.post('/dataset-groups/move', params, { useForm: true })
  },
}
