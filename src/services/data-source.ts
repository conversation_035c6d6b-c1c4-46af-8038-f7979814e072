import type { ListParams } from '~/types/common'
import apiClient from '~/utils/request'

export interface DataSourceDetail {
  createBy: string // 创建者
  createTime: string // 创建时间
  id: number // 项目ID
  name: string // 项目名称
  updateBy: string // 更新者
  updateTime: string // 更新时间
  owner: string // 归属
  description: string // 项目描述
  type: string // 数据源类型
}
export interface DataSourceCreate {
  name: string // 数据源名称
  dbName: string // 数据库名称
  type: string // 数据源类型
  // catalog: string // 目录
  url: string // 连接地址
  username: string // 用户名
  password: string // 密码
  config: unknown // 其他配置信息
  remark: string // 备注
}
export interface DataSourceUpdate extends DataSourceCreate {
  id: number
}
export interface DataSourceParams extends ListParams {
  page: unknown
  name?: string
}

// 数据源信息
export const dataSourceService = {
  list: (params: DataSourceParams) => {
    return apiClient.get('/data_source/list', params) as Promise<{ list: DataSourceDetail[], [p: string]: any }>
  },
  selectList: () => {
    return apiClient.get('/data_source/limit/select')
  },
  // 通过名称查询列表（下拉列表）
  listByName: (params: { dsName: string }) => {
    return apiClient.get('/data_source/name/select', params)
  },
  query: (id: DataSourceUpdate['id']) => {
    return apiClient.get(`/data_source/select/${id}`) as Promise<DataSourceDetail>
  },
  create: (params: DataSourceCreate) => {
    return apiClient.post('/data_source/save', params)
  },
  update: (params: DataSourceUpdate) => {
    return apiClient.put(`/data_source/update`, params)
  },
  delete: (id: DataSourceUpdate['id']) => {
    return apiClient.delete(`/data_source/delete/${id}`)
  },
  test: (params: DataSourceCreate) => {
    return apiClient.post('/data_source/test', params)
  },
}
