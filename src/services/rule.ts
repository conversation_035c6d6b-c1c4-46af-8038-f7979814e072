import type { PartitionExpressionFromEnum } from '~/types/enum'
import apiClient from '~/utils/request'

// 试运行基础参数
interface BaseRulesRun {
  rulesetId: number
  partitionExpressionFrom: PartitionExpressionFromEnum
  ruleIds: number[]
}
// 试运行调度参数
type RulesRunBySchedule = BaseRulesRun & {
  scheduleId: number
}
// 试运行自定义参数
type RulesRunByCustom = BaseRulesRun & {
  partitionExpression: string
}

// 规则服务
export const ruleServices = {
  list: (params: any) => {
    return apiClient.get('/rule/list', params)
  },
  allList: (params: any) => apiClient.get('/rule/listByRulesetId', params),
  query: (id: number) => {
    return apiClient.get(`/rule/select/${id}`)
  },
  create: (params: any) => {
    return apiClient.post('/rule/save', params)
  },
  update: (params: any) => {
    return apiClient.post('/rule/update', params)
  },
  delete: (id: number) => {
    return apiClient.post(`/rule/delete`, { id }, { useForm: true })
  },
  // 变更有效状态
  switch: (params: { id: number, valid: boolean }) => {
    return apiClient.post(`/rule/validFlag/update`, params, { useForm: true })
  },
  // 试运行
  run: (params: RulesRunBySchedule | RulesRunByCustom) => {
    return apiClient.post(`/rule/trial_run`, params)
  },
}

// 规则模板服务
export const ruleTemplateServices = {
  list: (catalog: string) => {
    return apiClient.get('/rule_template/catalog/select', { catalog })
  },

}

export interface RuleScheduleCreateParams {
  rulesetId: number // 规则集id
  scheduleName: string // 调度名称
  cronExpression: string // cron表达式
  scheduleType: string // 调度类型（例如：PERIOD_SCHEDULE-定时调度、COMPENSATE_SCHEDULE-补偿调度）
}

export interface RuleScheduleUpdateParams extends RuleScheduleCreateParams {
  id: number // 调度id
}

// 规则-调度服务
export const ruleScheduleServices = {
  // 根据规则集id查询调度列表
  list: (params: any) => {
    return apiClient.get('/rule_schedule/ruleset/select', params)
  },
  create: (params: RuleScheduleCreateParams) => {
    return apiClient.post('/rule_schedule/save', params)
  },
  update: (params: RuleScheduleUpdateParams) => {
    return apiClient.post('/rule_schedule/update', params)
  },
  detail: (id: number) => {
    return apiClient.get(`/rule_schedule/select/${id}`)
  },
  delete: (id: number) => {
    return apiClient.delete(`/rule_schedule/delete?scheduleId=${id}`)
  },
}

// 规则-告警
export const ruleWarningServices = {
  // 根据规则集ID查询规则告警配置信息
  list: (params: any) => apiClient.get('/rule-alert-config/rulesetId/list', params),
  // 保存规则告警配置信息
  save: (params: any) => apiClient.post('/rule-alert-config/save', params),
  // 更新规则告警配置信息
  update: (params: any) => apiClient.put('/rule-alert-config/update', params),
  // 根据ID删除规则告警配置信息
  delete: (id: number) => apiClient.delete(`/rule-alert-config/delete/${id}`),
}
