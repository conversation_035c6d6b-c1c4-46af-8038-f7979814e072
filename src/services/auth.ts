import apiClient from '~/utils/request'

export interface LoginParams {
  username: string
  password: string
}
export const authService = {
  // 登录
  login: (params: LoginParams) => {
    return apiClient.post('/auth/login', params)
  },
  // 获取用户信息
  getUser: () => {
    return apiClient.post('/auth/select/user')
  },
  // 刷新token
  refreshToken: () => {
    return apiClient.post('/auth/refresh/token')
  },
  // 用户列表
  userList: (params: any) => {
    return apiClient.post('/auth/select/users', params)
  },
}
