import apiClient from '~/utils/request'

// 校验记录列表项
export interface RuleValidateOne {
  batchNumber: string // 批次号
  id: number
  rulesetId: number
  rulesetName: string
  qualityOwner: string | null
  startTime: string
  endTime: string
  scheduleId: number // 调度ID
  scheduleName: string // 调度名称
}

export const ruleSetValidateService = {
  // 列表
  list: (params: { date: string, keyword: string }) => apiClient.get('/ruleset-validate-log/list', params),
  // 详情
  detail: (batchNumber: string) => apiClient.get('/rule-validate-log/batchNumber/select', { batchNumber }),
}

// 异常数据服务
export const ruleExceptionService = {
  // 根据批次号查询异常数据列表
  listByBatchNo: (params: any) => apiClient.get('/rule-exception-data/getByBatchNo', params),
  // 查询批次日志内容
  queryBatchLogContent: (batchNumber: string) => apiClient.get(`/rule-job-log/${batchNumber}`),
  // 查询规则日志内容
  queryRuleLogContent: (batchNumber: string, ruleId: string) => apiClient.get(`/rule-job-log/${batchNumber}/rules/${ruleId}`),
  // 查询规则日志内容左侧列表
  queryRuleLogSideList: (batchNumber: string) => apiClient.get(`/rule-job-log/batch-files/${batchNumber}`),
}
