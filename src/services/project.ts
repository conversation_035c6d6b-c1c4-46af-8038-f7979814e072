import type { ListParams } from '~/types/common'
import apiClient from '~/utils/request'

export interface ProjectDetail {
  createBy: string // 创建者
  createTime: string // 创建时间
  id: number // 项目ID
  name: string // 项目名称
  updateBy: string // 更新者
  updateTime: string // 更新时间
  ownerName: string // 归属
  description: string // 项目描述
}
export interface ProjectCreate {
  name: string // 项目名称
  description: string // 项目描述
}
export interface ProjectUpdate extends ProjectCreate {
  id: number
}
export interface ProjectParams extends ListParams {
  page: unknown
  name?: string
}

// 项目信息
export const projectService = {
  list: (params: ProjectParams) => {
    return apiClient.get('/project/list', params) as Promise<{ list: ProjectDetail[], [p: string]: any }>
  },
  query: (id: ProjectUpdate['id']) => {
    return apiClient.get(`/project/select/${id}`) as Promise<ProjectDetail>
  },
  create: (params: ProjectCreate) => {
    return apiClient.post('/project/save', params)
  },
  update: (params: ProjectUpdate) => {
    return apiClient.put(`/project/update`, params)
  },
  delete: (id: ProjectUpdate['id']) => {
    return apiClient.delete(`/project/delete/${id}`)
  },
  selectList: () => {
    return apiClient.get('/project/limit/select')
  },
}
