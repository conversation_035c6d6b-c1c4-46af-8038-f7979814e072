import CryptoJS from 'crypto-js'
// 加密函数
// 加密函数
export function encrypt(plainText: string, secretKey: string, iv: string) {
  const key = CryptoJS.enc.Utf8.parse(secretKey) // 32 字节的密钥
  const ivBytes = CryptoJS.enc.Utf8.parse(iv) // 16 字节的初始化向量（IV）

  const encrypted = CryptoJS.AES.encrypt(plainText, key, {
    iv: ivBytes,
    padding: CryptoJS.pad.Pkcs7, // 填充方式
    mode: CryptoJS.mode.ECB, // 加密模式
  })

  return encrypted.toString() // 返回加密后的结果（Base64 编码）
}

// 解密函数
// 解密函数
export function decrypt(cipherText: string, secretKey: string, iv: string) {
  const key = CryptoJS.enc.Utf8.parse(secretKey) // 32 字节的密钥
  const ivBytes = CryptoJS.enc.Utf8.parse(iv) // 16 字节的初始化向量（IV）

  const decrypted = CryptoJS.AES.decrypt(cipherText, key, {
    iv: ivBytes,
    padding: CryptoJS.pad.Pkcs7, // 填充方式
    mode: CryptoJS.mode.ECB, // 加密模式
  })

  return decrypted.toString(CryptoJS.enc.Utf8) // 解密后的明文
}
