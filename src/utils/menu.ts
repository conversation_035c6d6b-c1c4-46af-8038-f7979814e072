import type { RouteLocation } from 'vue-router'
import type { Menu, MenuItem } from '~/enums/menu'

/**
 * 从树结构中查找对应 path 的 title
 * @param {Array} tree - 树形结构数据
 * @param {string} path - 要查找的路径
 * @returns {string | null} - 对应路径的标题，如果未找到则返回 null
 */
export function findRouteByPath(
  tree: Menu,
  route: RouteLocation,
): MenuItem[] | null {
  for (const node of tree) {
    // 如果当前节点路径匹配目标路径
    if (isTargetRoute(route, node)) {
      node.currentRoute = route.path
      // 返回当前路由的标题作为面包屑的末尾
      return [node]
    }

    // 如果当前节点有子路由，则递归查找
    if (node.children) {
      const result = findRouteByPath(node.children, route)
      if (result) {
        // 如果找到子路由，则拼接当前路由的标题
        return [node, ...result]
      }
    }
  }

  return null // 如果未找到目标路由
}

// TODO: 优化查询逻辑
// 查找当前路由对应的父级菜单(1\2级不返回，3级+返回父级菜单)
export function findMenuParentByRoute(route: RouteLocation, menus: Menu) {
  for (const menu of menus) {
    if (!menu.children)
      continue

    for (const child of menu.children) {
      if (!child.children)
        continue

      for (const children of child.children) {
        if (isTargetRoute(route, children)) {
          return child
        }

        if (!children.children)
          return
        for (const grandChildren of children.children) {
          if (isTargetRoute(route, grandChildren)) {
            return child
          }
        }
      }
    }
  }
}

function isTargetRoute(route: RouteLocation, menu: MenuItem): boolean {
  return menu.path === route.path || menu.path === route.name
}
