class WebFunny {
  constructor() {
  }

  // 设置用户信息
  setUser<T extends{ userId: string }>(user?: T | null) {
    if (import.meta.env.VITE_RUN_ENV === 'porsche') {
      window.localStorage.setItem('wmUserInfo', JSON.stringify({
        userId: user?.userId,
        projectVersion: '1.0.1',
        env: import.meta.env.VITE_WEB_FUNNY_ENV,
      }))
    }
  }
}

const webFunny = new WebFunny()
export default webFunny
