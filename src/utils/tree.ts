// 是否是文件夹
export function isFolder<T extends { nodeType: '1' | '0' }>(item: T) {
  return item.nodeType === '1'
}

interface Tree {
  id: string | number
  children?: Tree[]
  parentId: string | number
}

/**
 * 通过 id 查找树中某个节点
 * @param {Array} tree - 树形结构
 * @param {string | number} id - 要查找的节点 id
 * @returns {object | null} - 找到的节点，未找到返回 null
 */
export function findNodeById(tree: Tree[], id: string | number): Tree | null {
  for (const node of tree) {
    if (node.id === id) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const result = findNodeById(node.children, id)
      if (result) {
        return result
      }
    }
  }
  return null
}
