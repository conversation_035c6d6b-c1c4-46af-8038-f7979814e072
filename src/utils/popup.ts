import type { ElMessageBoxOptions } from 'element-plus'
import { ElMessageBox } from 'element-plus'

export function popUp(content: string, options: Partial<ElMessageBoxOptions> & { title?: string } = {}): Promise<boolean> {
  const baseConfig = {
    showClose: false,
    customClass: 'custom-popup',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    title: '提示',
    closeOnClickModal: false,
  }

  // 首先合并基础配置和传入的选项
  const mergedConfig = {
    ...baseConfig,
    ...options,
  }

  // 设置按钮显示
  mergedConfig.showCancelButton = options.showCancelButton ?? Boolean(mergedConfig.cancelButtonText)
  mergedConfig.showConfirmButton = options.showConfirmButton ?? Boolean(mergedConfig.confirmButtonText)

  // 设置弹窗类型
  mergedConfig.type = mergedConfig.showCancelButton && mergedConfig.showConfirmButton ? 'warning' : 'info'

  return new Promise((resolve) => {
    ElMessageBox.confirm(content, mergedConfig.title ?? '', mergedConfig)
      .then(() => resolve(true))
      .catch(() => resolve(false))
  })
}

export function alert(content: string, options?: Partial<ElMessageBoxOptions> & { title?: string }): Promise<boolean> {
  return popUp(content, { ...options, showCancelButton: false }) // 确保alert没有取消按钮
}
