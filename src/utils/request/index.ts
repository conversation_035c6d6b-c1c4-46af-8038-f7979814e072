import type { InternalAxiosRequestConfig, Method } from 'axios'
import axios from 'axios'
import { ElNotification } from 'element-plus'
import qs from 'qs'
import { getToken } from '../token'
import { getBasePrefix } from './base-url'
import { handleAuthError } from './handler'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: '', // 根据实际需求配置
  timeout: 60000, // 超时时间
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig<any> & { useForm?: boolean }) => {
    config.baseURL = getBasePrefix(config.url || '')

    // 登录接口不需要携带 token 参数，其他接口都需要携带
    if (getToken() && config.url !== '/auth/login') {
      config.headers.Authorization = `Bearer ${getToken()}`
    }

    // 在这里可以对请求参数做统一处理
    if (config.method === 'get') {
      config.params = config.data // Axios get 方法需使用 params 传参
    }

    // 简化表单提交逻辑，通过 useForm 配置项启用
    if (config.useForm) {
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
    }

    // 动态设置 Content-Type 和请求体格式
    if (config.headers['Content-Type'] === 'application/x-www-form-urlencoded') {
      config.data = qs.stringify(config.data)
    }
    else if (config.headers['Content-Type'] === 'multipart/form-data') {
      const formData = new FormData()
      Object.entries(config.data || {}).forEach(([key, value]) => {
        formData.append(key, value as any)
      })
      config.data = formData
    }

    return config
  },
  error => Promise.reject(error),
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    if (response?.data?.code === '0000') {
      return response.data?.result
    }
    else {
      ElNotification.error(`${response.data.message}`)
      return Promise.reject(response)
    }
  }, // 简化响应数据
  async (error) => {
    // TOKEN 过期处理
    if (error.status === 401) {
      handleAuthError(request, error)
    }
    // 可以在这里处理统一的错误逻辑
    console.error('API Error:', error.response || error.message)
    return Promise.reject(error)
  },
)

// 核心方法封装
function request(method: Method, url: string, params = {}, config = {}) {
  return apiClient({
    method,
    url,
    data: params, // 默认放在 data 中
    ...config, // 合并用户自定义配置
  })
}

// 各种 HTTP 方法封装
export default {
  get(url: string, params = {}, config = {}) {
    return request('get', url, params, config) as Promise<any>
  },
  post(url: string, params = {}, config = {}) {
    return request('post', url, params, config) as Promise<any>
  },
  put(url: string, params = {}, config = {}) {
    return request('put', url, params, config) as Promise<any>
  },
  delete(url: string, params = {}, config = {}) {
    return request('delete', url, params, config) as Promise<any>
  },
}
