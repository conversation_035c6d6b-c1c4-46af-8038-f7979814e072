import { ElNotification } from 'element-plus'
import { useUserStore } from '~/stores/user'

const refreshTokenUrl = '/auth/refresh/token'

/**
 * 处理认证错误的逻辑401
 * @param request 请求方法（传入，否则会造成循环引用）
 * @param error 错误信息
 */
export async function handleAuthError(request: any, error: any) {
  // 如果是刷新token的请求，则直接登出回到登录页面
  if (error.config.url === refreshTokenUrl) {
    useUserStore().logout()
    ElNotification.error({
      title: '登录过期',
      message: '请重新登录',
    })
    window.location.href = '/'
  }
  else {
    // 直接引用service回造成循环引用
    await request('post', refreshTokenUrl)
  }
}
