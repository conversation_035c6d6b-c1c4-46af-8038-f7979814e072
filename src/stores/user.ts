import { removeToken } from '~/utils/token'
import webFunny from '~/utils/webfunny'

export interface User {
  appId: string
  userName: string
  userId: string
  email: string
}

export const useUserStore = defineStore('user', {
  state: (): {
    user: User | null
  } => ({
    user: null,
  }),
  actions: {
    setUser(user: User) {
      this.user = user
      webFunny.setUser(user)
    },
    logout(fn?: () => void) {
      removeToken()
      this.user = null
      webFunny.setUser(null)
      fn?.()
    },
  },
})
