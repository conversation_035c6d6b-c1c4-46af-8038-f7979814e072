import { ElCol, ElRow } from 'element-plus'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { createApp } from 'vue'
import App from './App.vue'
import { router } from './routers'

import '@unocss/reset/tailwind.css'
import 'nprogress/nprogress.css'
import './styles/main.scss'
import 'uno.css'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

const app = createApp(App)

// 动态表单未明确引入组件
app.component('el-row', ElRow)
app.component('el-col', ElCol)

app.use(pinia)
app.use(router)
app.mount('#app')
