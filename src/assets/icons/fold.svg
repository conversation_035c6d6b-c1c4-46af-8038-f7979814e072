<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="14px" viewBox="0 0 48 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>折叠</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="数据集-编辑" transform="translate(-1049, -325)">
            <g id="折叠" transform="translate(1072.9962, 332) rotate(-360) translate(-1072.9962, -332)translate(1048.99, 325)">
                <rect id="矩形" stroke="#909399" stroke-width="0.5" fill="#FFFFFF" x="0.25" y="0.25" width="47.5123555" height="13.5" rx="6.75"></rect>
                <path d="M29.1130166,11.878607 C28.9101416,12.054584 28.6030178,12.0327008 28.4270408,11.8296739 L24.140072,6.79381517 C24.0792855,6.72375871 23.9703256,6.72391068 23.909995,6.79442303 L19.5868582,11.8304337 C19.411945,12.0342205 19.1049731,12.0576233 18.9011863,11.8827101 C18.6973995,11.7077969 18.6739967,11.400825 18.8489099,11.1970382 L23.7916133,5.43918772 C23.9125784,5.29831497 24.1303461,5.29785907 24.2519192,5.43812395 L29.1619498,11.1927832 C29.3377748,11.3955062 29.3158916,11.70263 29.1130166,11.878607 L29.1130166,11.878607 Z M29.1130166,8.54522658 C28.9101416,8.72120354 28.6030178,8.69932039 28.4270408,8.49629344 L24.140072,3.46043474 C24.0792855,3.39037829 23.9703256,3.39053025 23.909995,3.46104261 L19.5868582,8.49705327 C19.411945,8.70084005 19.1049731,8.72424286 18.9011863,8.54932967 C18.6973995,8.37441648 18.6739967,8.06744458 18.8489099,7.8636578 L23.7916133,2.10595926 C23.9125784,1.96508652 24.1303461,1.96463062 24.2519192,2.1048955 L29.1619498,7.85955471 C29.3377748,8.06212576 29.3158916,8.36924962 29.1130166,8.54522658 L29.1130166,8.54522658 Z" id="形状" fill="#909399" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>
