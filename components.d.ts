/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AbnormalDataDialog: typeof import('./src/pages/records/rule-validate-log/components/AbnormalDataDialog.vue')['default']
    BaseBread: typeof import('./src/components/BaseBread.vue')['default']
    BaseContent: typeof import('./src/components/BaseContent.vue')['default']
    BaseDialog: typeof import('./src/components/BaseDialog.vue')['default']
    BaseDrawer: typeof import('./src/components/BaseDrawer.vue')['default']
    BaseEmpty: typeof import('./src/components/BaseEmpty.vue')['default']
    BaseHeader: typeof import('./src/components/BaseHeader.vue')['default']
    BaseTable: typeof import('./src/components/BaseTable/index.vue')['default']
    BaseTitle: typeof import('./src/components/BaseTitle.vue')['default']
    BaseTree: typeof import('./src/components/BaseTree/index.vue')['default']
    CodeEditor: typeof import('./src/components/CodeEditor.vue')['default']
    ConfirmDelete: typeof import('./src/components/ConfirmDelete.vue')['default']
    CreateFolder: typeof import('./src/pages/rules/data-set/components/CreateFolder.vue')['default']
    CustomArea: typeof import('./src/components/DynamicForm/components/CustomArea.vue')['default']
    CustomAsyncSelect: typeof import('./src/components/DynamicForm/components/CustomAsyncSelect.vue')['default']
    CustomCascader: typeof import('./src/components/DynamicForm/components/CustomCascader.vue')['default']
    CustomCheckbox: typeof import('./src/components/DynamicForm/components/CustomCheckbox.vue')['default']
    CustomDate: typeof import('./src/components/DynamicForm/components/CustomDate.vue')['default']
    CustomDatePicker: typeof import('./src/components/DynamicForm/components/CustomDatePicker.vue')['default']
    CustomInput: typeof import('./src/components/DynamicForm/components/CustomInput.vue')['default']
    CustomInputNumber: typeof import('./src/components/DynamicForm/components/CustomInputNumber.vue')['default']
    CustomRadio: typeof import('./src/components/DynamicForm/components/CustomRadio.vue')['default']
    CustomSelect: typeof import('./src/components/DynamicForm/components/CustomSelect.vue')['default']
    CustomSelectV2: typeof import('./src/components/DynamicForm/components/CustomSelectV2.vue')['default']
    CustomShow: typeof import('./src/components/DynamicForm/components/CustomShow.vue')['default']
    CustomSwitch: typeof import('./src/components/DynamicForm/components/CustomSwitch.vue')['default']
    CustomTreeSelect: typeof import('./src/components/DynamicForm/components/CustomTreeSelect.vue')['default']
    DataSetBaseForm: typeof import('./src/pages/rules/data-set/components/DataSetBaseForm.vue')['default']
    DataSetList: typeof import('./src/pages/rules/data-set/components/DataSetList.vue')['default']
    DataSetOverview: typeof import('./src/pages/rules/data-set/components/DataSetOverview.vue')['default']
    DataSetRunConfig: typeof import('./src/pages/rules/data-set/components/DataSetRunConfig.vue')['default']
    DataSetSideMenu: typeof import('./src/pages/rules/data-set/components/DataSetSideMenu.vue')['default']
    DataSetTree: typeof import('./src/pages/rules/data-set/components/DataSetTree.vue')['default']
    DataSourceDetail: typeof import('./src/pages/data-source/components/DataSourceDetail.vue')['default']
    DescribeCard: typeof import('./src/components/DescribeCard.vue')['default']
    DialogMultiFieldCompare: typeof import('./src/pages/rules/rule-set/components/rules/DialogMultiFieldCompare.vue')['default']
    DynamicForm: typeof import('./src/components/DynamicForm/DynamicForm.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElInputTag: typeof import('element-plus/es')['ElInputTag']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSelectV2: typeof import('element-plus/es')['ElSelectV2']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    FooterPagination: typeof import('./src/components/FooterPagination.vue')['default']
    GlobalPopover: typeof import('./src/components/GlobalPopover.vue')['default']
    ProjectDetail: typeof import('./src/pages/rules/project/components/ProjectDetail.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RulesBaseInfo: typeof import('./src/pages/rules/rule-set/components/rules/RulesBaseInfo.vue')['default']
    RulesConfig: typeof import('./src/pages/rules/rule-set/components/rules/RulesConfig.vue')['default']
    RulesDispatch: typeof import('./src/pages/rules/rule-set/components/rules/RulesDispatch.vue')['default']
    RuleSetEdit: typeof import('./src/pages/rules/rule-set/components/RuleSetEdit.vue')['default']
    RuleSetRun: typeof import('./src/pages/rules/rule-set/components/RuleSetRun.vue')['default']
    RulesFieldSelect: typeof import('./src/pages/rules/rule-set/components/RulesFieldSelect.vue')['default']
    RulesFilter: typeof import('./src/pages/rules/rule-set/components/rules/RulesFilter.vue')['default']
    RulesMantis: typeof import('./src/pages/rules/rule-set/components/rules/RulesMantis.vue')['default']
    RulesMonitor: typeof import('./src/pages/rules/rule-set/components/rules/RulesMonitor.vue')['default']
    RulesOther: typeof import('./src/pages/rules/rule-set/components/rules/RulesOther.vue')['default']
    RulesRun: typeof import('./src/pages/rules/rule-set/components/rules/RulesRun.vue')['default']
    RuleTemplateCascader: typeof import('./src/pages/rules/rule-set/components/RuleTemplateCascader.vue')['default']
    RuleValidateLogDetail: typeof import('./src/pages/records/rule-validate-log/components/RuleValidateLogDetail.vue')['default']
    RuleValidateLogDialog: typeof import('./src/pages/records/rule-validate-log/components/RuleValidateLogDialog.vue')['default']
    SearchForm: typeof import('./src/components/SearchForm.vue')['default']
    SearchTable: typeof import('./src/components/SearchTable.vue')['default']
    SideMenu: typeof import('./src/components/SideMenu/index.vue')['default']
    SideMenuItem: typeof import('./src/components/SideMenu/SideMenuItem.vue')['default']
    TheFooter: typeof import('./src/components/TheFooter.vue')['default']
    TheInput: typeof import('./src/components/TheInput.vue')['default']
    TopHeader: typeof import('./src/components/TopHeader.vue')['default']
    WarningDetail: typeof import('./src/pages/rules/rule-set/components/warning/WarningDetail.vue')['default']
    WarningList: typeof import('./src/pages/rules/rule-set/components/warning/WarningList.vue')['default']
    WayField: typeof import('./src/pages/rules/rule-set/components/rules/ways/WayField.vue')['default']
    WayMulti: typeof import('./src/pages/rules/rule-set/components/rules/ways/WayMulti.vue')['default']
    WayStability: typeof import('./src/pages/rules/rule-set/components/rules/ways/WayStability.vue')['default']
    WayType: typeof import('./src/pages/rules/rule-set/components/rules/ways/WayType.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
