/// <reference types="vitest" />

import path from 'node:path'
import process from 'node:process'
import Vue from '@vitejs/plugin-vue'
import { codeInspectorPlugin } from 'code-inspector-plugin'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import ElementPlus from 'unplugin-element-plus/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { VueRouterAutoImports } from 'unplugin-vue-router'
import VueRouter from 'unplugin-vue-router/vite'
import { defineConfig, loadEnv } from 'vite'
import { createHtmlPlugin } from 'vite-plugin-html'
import Layouts from 'vite-plugin-vue-layouts'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return { server: {
    proxy: {
      '/data-quality': {
        //target: 'http://porsche-data-quality.chinahuanong.com.cn',
         target: 'http://127.0.0.1:8257',
        changeOrigin: true,
      },
    },
  }, resolve: {
    alias: {
      '~/': `${path.resolve(__dirname, 'src')}/`,
    },
  }, css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "~/styles/element.scss" as *;`,
        api: 'modern-compiler',
      },
    },
  }, plugins: [
    Vue(),

    // https://github.com/JohnCampionJr/vite-plugin-vue-layouts
    Layouts(),

    // https://github.com/posva/unplugin-vue-router
    VueRouter({
      exclude: ['**/*.component.vue', '**/components/**', '**/component/**'],
      extensions: ['.vue'],
    }),

    createHtmlPlugin({
      inject: {
        data: {
          loadWebFunny: env.VITE_RUN_ENV === 'porsche', // 是否加载webFunny (设置用户信息处有相同逻辑)
          env: env.VITE_RUN_ENV,
          webFunnyEnv: env.VITE_WEB_FUNNY_ENV,
        },
      },
    }),

    // https://github.com/antfu/unplugin-auto-import
    AutoImport({
      imports: [
        'vue',
        '@vueuse/core',
        'pinia',
        VueRouterAutoImports,
        {
          // add any other imports you were relying on
          'vue-router/auto': ['useLink'],
        },
      ],
      resolvers: [ElementPlusResolver({
        importStyle: 'sass',
      })],
      dts: true,
      dirs: [
        './src/composables',
        './src/stores',
      ],
      vueTemplate: true,
    }),

    ElementPlus({
      useSource: true,
      defaultLocale: 'zh-cn',
    }),

    // https://github.com/antfu/vite-plugin-components
    Components({
      dts: true,
      dirs: ['src/components', 'src/pages/**/components/**', '**/*.component.vue'],
      resolvers: [ElementPlusResolver({
        importStyle: 'sass',
      })],
    }),

    // https://github.com/antfu/unocss
    // see uno.config.ts for config
    UnoCSS(),
    codeInspectorPlugin({
      bundler: 'vite',
    }),
  ],

  // https://github.com/vitest-dev/vitest
  test: {
    environment: 'jsdom',
  }	}
})
