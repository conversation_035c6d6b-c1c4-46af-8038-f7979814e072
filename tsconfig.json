{"compilerOptions": {"target": "es2020", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"~/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "unplugin-vue-macros/macros-global", "unplugin-vue-router/client", "vite-plugin-vue-layouts/client"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "exclude": ["dist", "node_modules", "eslint.config.js"]}