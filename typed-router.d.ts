/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/[...all]': RouteRecordInfo<'/[...all]', '/:all(.*)', { all: ParamValue<true> }, { all: ParamValue<false> }>,
    '/data-source/': RouteRecordInfo<'/data-source/', '/data-source', Record<never, never>, Record<never, never>>,
    '/hi/[name]': RouteRecordInfo<'/hi/[name]', '/hi/:name', { name: ParamValue<true> }, { name: ParamValue<false> }>,
    '/login': RouteRecordInfo<'/login', '/login', Record<never, never>, Record<never, never>>,
    '/records/rule-validate-log/': RouteRecordInfo<'/records/rule-validate-log/', '/records/rule-validate-log', Record<never, never>, Record<never, never>>,
    '/rules/data-set/': RouteRecordInfo<'/rules/data-set/', '/rules/data-set', Record<never, never>, Record<never, never>>,
    '/rules/data-set/edit/[id]': RouteRecordInfo<'/rules/data-set/edit/[id]', '/rules/data-set/edit/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/rules/project/': RouteRecordInfo<'/rules/project/', '/rules/project', Record<never, never>, Record<never, never>>,
    '/rules/rule-set/': RouteRecordInfo<'/rules/rule-set/', '/rules/rule-set', Record<never, never>, Record<never, never>>,
    '/rules/rule-set/detail/[id]': RouteRecordInfo<'/rules/rule-set/detail/[id]', '/rules/rule-set/detail/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/rules/rule-set/rule-config/create': RouteRecordInfo<'/rules/rule-set/rule-config/create', '/rules/rule-set/rule-config/create', Record<never, never>, Record<never, never>>,
    '/rules/rule-set/rule-config/edit/[id]': RouteRecordInfo<'/rules/rule-set/rule-config/edit/[id]', '/rules/rule-set/rule-config/edit/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/schedule/': RouteRecordInfo<'/schedule/', '/schedule', Record<never, never>, Record<never, never>>,
  }
}
