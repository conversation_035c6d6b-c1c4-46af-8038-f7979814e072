{"mcpServers": {"@21st-dev-magic-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@21st-dev/magic-mcp", "--config", "{\"TWENTY_FIRST_API_KEY\":\"6975283451be3efa522b0ab523f4da766dc09a4348649092c7911eb6f8aaadce\"}"]}, "github.com/GLips/Figma-Context-MCP": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--st<PERSON>"], "env": {"FIGMA_API_KEY": "*********************************************"}, "disabled": false, "autoApprove": []}}}